export default {
    expense: {
        // 分类管理
        category: {
            name: '分类名称',
            type: '分类类型',
            icon: '分类图标',
            color: '分类颜色',
            sort: '排序权重',
            income: '收入',
            expense: '支出',
            management: '分类管理',
            add_category: '添加分类',
            edit_category: '编辑分类',
            delete_category: '删除分类',
            category_list: '分类列表',
            default_categories: '默认分类',
            custom_category: '自定义分类',
        },
        // 记录管理
        record: {
            user: '用户',
            category: '分类',
            type: '类型',
            amount: '金额',
            description: '备注说明',
            record_date: '记录日期',
            record_time: '记录时间',
            tags: '标签',
            attachment: '附件',
            total_income: '总收入',
            total_expense: '总支出',
            balance: '结余',
            records: '笔',
            amount_min: '金额必须大于0.01',
            management: '记录管理',
            add_record: '添加记录',
            edit_record: '编辑记录',
            delete_record: '删除记录',
            record_list: '记录列表',
            income_record: '收入记录',
            expense_record: '支出记录',
            recent_records: '最近记录',
            search_records: '搜索记录',
            filter_by_date: '按日期筛选',
            filter_by_category: '按分类筛选',
            filter_by_type: '按类型筛选',
            no_records: '暂无记录',
        },
        // 统计分析 
        statistics: {
            title: '收支统计',
            overview: '概览',
            category_distribution: '分类分布',
            monthly_trend: '月度趋势',
            daily_trend: '日趋势',
            yearly_trend: '年度趋势',
            income_trend: '收入趋势',
            expense_trend: '支出趋势',
            balance_trend: '结余趋势',
            top_categories: '热门分类',
            recent_analysis: '近期分析',
            export_data: '导出数据',
            export_success: '导出成功',
            export_failed: '导出失败',
            date_range: '日期范围',
            start_date: '开始日期',
            end_date: '结束日期',
            this_month: '本月',
            last_month: '上月',
            this_year: '今年',
            last_year: '去年',
            custom_range: '自定义范围',
        },
        // 通用
        common: {
            expense_management: '收支管理',
            financial_management: '财务管理',
            money_management: '资金管理',
            income_and_expense: '收入支出',
            daily_expense: '日常开支',
            personal_finance: '个人理财',
            budget_management: '预算管理',
            financial_analysis: '财务分析',
            cash_flow: '现金流',
            financial_report: '财务报表',
            currency_symbol: '¥',
            amount_format: '金额格式',
            date_format: '日期格式',
            time_format: '时间格式',
        },
        // 消息提示
        messages: {
            add_success: '添加成功',
            edit_success: '修改成功',
            delete_success: '删除成功',
            add_failed: '添加失败',
            edit_failed: '修改失败',
            delete_failed: '删除失败',
            load_failed: '加载数据失败',
            save_success: '保存成功',
            save_failed: '保存失败',
            operation_success: '操作成功',
            operation_failed: '操作失败',
            confirm_delete: '确定要删除这条记录吗？',
            confirm_batch_delete: '确定要删除选中的记录吗？',
            no_data: '暂无数据',
            loading: '加载中...',
            processing: '处理中...',
        },
        // 验证消息
        validation: {
            required_field: '此字段为必填项',
            invalid_amount: '请输入有效的金额',
            invalid_date: '请选择有效的日期',
            invalid_time: '请选择有效的时间',
            amount_too_small: '金额必须大于0',
            amount_too_large: '金额过大',
            description_too_long: '备注说明过长',
            invalid_category: '请选择有效的分类',
            invalid_type: '请选择有效的类型',
        },
    },
}
