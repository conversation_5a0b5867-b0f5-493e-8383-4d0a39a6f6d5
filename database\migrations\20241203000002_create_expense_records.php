<?php

use think\migration\Migrator;
use Phinx\Db\Adapter\MysqlAdapter;

class CreateExpenseRecords extends Migrator
{
    /**
     * 创建收支记录表
     */
    public function change(): void
    {
        $table = $this->table('expense_records', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '收支记录表',
        ]);

        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '记录ID'
        ])
        ->addColumn('user_id', 'integer', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('category_id', 'integer', [
            'signed' => false,
            'null' => false,
            'comment' => '分类ID'
        ])
        ->addColumn('type', 'enum', [
            'values' => ['income', 'expense'],
            'null' => false,
            'comment' => '记录类型:income=收入,expense=支出'
        ])
        ->addColumn('amount', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '金额(分为单位)'
        ])
        ->addColumn('description', 'string', [
            'limit' => 255,
            'null' => true,
            'default' => '',
            'comment' => '备注说明'
        ])
        ->addColumn('record_date', 'date', [
            'null' => false,
            'comment' => '记录日期'
        ])
        ->addColumn('record_time', 'time', [
            'null' => true,
            'comment' => '记录时间'
        ])
        ->addColumn('tags', 'string', [
            'limit' => 255,
            'null' => true,
            'default' => '',
            'comment' => '标签(JSON格式)'
        ])
        ->addColumn('attachment', 'text', [
            'null' => true,
            'comment' => '附件(JSON格式)'
        ])
        ->addColumn('create_time', 'biginteger', [
            'signed' => false,
            'null' => true,
            'comment' => '创建时间'
        ])
        ->addColumn('update_time', 'biginteger', [
            'signed' => false,
            'null' => true,
            'comment' => '更新时间'
        ])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['category_id'], ['name' => 'idx_category_id'])
        ->addIndex(['type'], ['name' => 'idx_type'])
        ->addIndex(['record_date'], ['name' => 'idx_record_date'])
        ->addIndex(['user_id', 'record_date'], ['name' => 'idx_user_date'])
        ->addForeignKey('user_id', 'user', 'id', [
            'delete' => 'CASCADE',
            'update' => 'CASCADE'
        ])
        ->addForeignKey('category_id', 'expense_categories', 'id', [
            'delete' => 'RESTRICT',
            'update' => 'CASCADE'
        ])
        ->create();
    }
}
