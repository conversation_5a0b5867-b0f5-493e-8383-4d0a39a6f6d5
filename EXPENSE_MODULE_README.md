# 日常收支管理模块

基于 BuildAdmin 框架开发的日常收支管理模块，用于管理个人或家庭的日常收入和支出记录。

## 功能特性

### 🏷️ 分类管理
- 收入分类管理（工资、奖金、投资收益等）
- 支出分类管理（餐饮、交通、购物等）
- 支持自定义图标和颜色
- 分类排序和状态管理

### 📝 记录管理
- 收支记录的增删改查
- 支持按用户、分类、类型、日期等条件筛选
- 记录详细信息（金额、备注、标签、附件等）
- 实时统计显示（总收入、总支出、结余）

### 📊 统计分析
- 收支概览统计
- 分类分布饼图
- 月度趋势图表
- 日统计数据
- 数据导出功能

## 数据库设计

### 收支分类表 (expense_categories)
```sql
- id: 分类ID
- name: 分类名称
- type: 分类类型 (income/expense)
- icon: 分类图标
- color: 分类颜色
- sort: 排序权重
- status: 状态 (enable/disable)
- create_time: 创建时间
- update_time: 更新时间
```

### 收支记录表 (expense_records)
```sql
- id: 记录ID
- user_id: 用户ID
- category_id: 分类ID
- type: 记录类型 (income/expense)
- amount: 金额(分为单位)
- description: 备注说明
- record_date: 记录日期
- record_time: 记录时间
- tags: 标签(JSON)
- attachment: 附件(JSON)
- create_time: 创建时间
- update_time: 更新时间
```

## 安装步骤

### 1. 运行数据库迁移
```bash
# 进入项目根目录
cd /path/to/buildadmin

# 运行迁移命令
php think migrate:run
```

### 2. 安装前端依赖（如果需要）
```bash
# 进入web目录
cd web

# 安装依赖
pnpm install

# 构建前端资源
pnpm run build
```

### 3. 配置权限
- 登录后台管理系统
- 进入 权限管理 -> 菜单规则
- 确认收支管理相关菜单已正确添加
- 为相应的管理员组分配权限

### 4. 初始化分类数据
系统会自动创建以下默认分类：

**收入分类：**
- 工资收入
- 奖金
- 投资收益
- 兼职收入
- 礼金红包
- 其他收入

**支出分类：**
- 餐饮美食
- 交通出行
- 购物消费
- 生活缴费
- 医疗健康
- 学习教育
- 娱乐休闲
- 服装美容
- 人情往来
- 投资理财
- 其他支出

## 使用说明

### 分类管理
1. 进入 收支管理 -> 分类管理
2. 可以添加、编辑、删除分类
3. 支持设置分类图标和颜色
4. 可以调整分类排序

### 记录管理
1. 进入 收支管理 -> 记录管理
2. 点击添加按钮创建新的收支记录
3. 选择用户、分类、类型，输入金额和备注
4. 可以设置记录日期和时间
5. 支持添加标签进行分类

### 统计分析
1. 进入 收支管理 -> 统计分析
2. 查看收支概览统计
3. 通过图表分析收支分布和趋势
4. 可以按日期范围筛选数据
5. 支持导出统计数据

## 文件结构

```
├── database/migrations/                    # 数据库迁移文件
│   ├── 20241203000001_create_expense_categories.php
│   ├── 20241203000002_create_expense_records.php
│   ├── 20241203000003_init_expense_categories_data.php
│   └── 20241203000004_add_expense_menu_rules.php
├── app/admin/model/                        # 后端模型
│   ├── ExpenseCategory.php
│   └── ExpenseRecord.php
├── app/admin/controller/expense/           # 后端控制器
│   ├── Category.php
│   ├── Record.php
│   └── Statistics.php
├── app/admin/validate/                     # 验证器
│   ├── ExpenseCategory.php
│   └── ExpenseRecord.php
├── web/src/views/backend/expense/          # 前端页面
│   ├── category/
│   │   ├── index.vue
│   │   └── popupForm.vue
│   ├── record/
│   │   ├── index.vue
│   │   └── popupForm.vue
│   └── statistics/
│       └── index.vue
└── web/src/lang/backend/                   # 语言包
    ├── zh-cn/expense.ts
    └── en/expense.ts
```

## API 接口

### 分类管理
- `GET /admin/expense.Category/` - 获取分类列表
- `POST /admin/expense.Category/add` - 添加分类
- `POST /admin/expense.Category/edit` - 编辑分类
- `DELETE /admin/expense.Category/del` - 删除分类
- `GET /admin/expense.Category/select` - 获取分类选项

### 记录管理
- `GET /admin/expense.Record/` - 获取记录列表
- `POST /admin/expense.Record/add` - 添加记录
- `POST /admin/expense.Record/edit` - 编辑记录
- `DELETE /admin/expense.Record/del` - 删除记录
- `GET /admin/expense.Record/statistics` - 获取统计数据

### 统计分析
- `GET /admin/expense.Statistics/getUserStats` - 用户统计
- `GET /admin/expense.Statistics/getCategoryStats` - 分类统计
- `GET /admin/expense.Statistics/getMonthlyTrend` - 月度趋势
- `GET /admin/expense.Statistics/getDailyStats` - 日统计
- `GET /admin/expense.Statistics/export` - 导出数据

## 注意事项

1. **金额存储**：系统使用分为单位存储金额，避免浮点数精度问题
2. **用户权限**：确保为用户分配正确的权限才能访问相应功能
3. **数据备份**：建议定期备份收支数据
4. **性能优化**：大量数据时建议添加适当的索引

## 扩展建议

1. **预算管理**：可以扩展预算设置和预警功能
2. **定期记录**：支持定期自动记录（如月租、工资等）
3. **多账户**：支持多个账户（现金、银行卡等）管理
4. **报表导出**：支持更多格式的报表导出
5. **移动端**：开发移动端应用或小程序

## 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue 到项目仓库
- 发送邮件到开发团队
- 加入技术交流群

---

**版本**: 1.0.0  
**更新时间**: 2024-12-03  
**兼容性**: BuildAdmin 2.3.3+
