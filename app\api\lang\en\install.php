<?php
return [
    'Install the controller'                                                                                             => 'Install the controller',
    'need'                                                                                                               => 'Need',
    'Click to see how to solve it'                                                                                       => 'Click to see how to solve.',
    'Please check the config directory permissions'                                                                      => 'Please check the Config directory permissions',
    'Please check the public directory permissions'                                                                      => 'Please check the Public directory permissions',
    'open'                                                                                                               => 'Open',
    'close'                                                                                                              => 'Close',
    'The installation can continue, and some operations need to be completed manually'                                   => 'You can continue to install, and some operations need to be completed manually ',
    'Allow execution'                                                                                                    => 'Allow execution',
    'disabled'                                                                                                           => 'Disabled',
    'Allow operation'                                                                                                    => 'Allow operation',
    'Acquisition failed'                                                                                                 => 'Access failed',
    'Click Install %s'                                                                                                   => 'Click Install %s',
    'Writable'                                                                                                           => 'Writable',
    'No write permission'                                                                                                => 'No write permissions',
    'already installed'                                                                                                  => 'Installed',
    'Not installed'                                                                                                      => 'Not installed',
    'File has no write permission:%s'                                                                                    => 'File has no write permission:%s',
    'The system has completed installation. If you need to reinstall, please delete the %s file first'                   => 'The system has been installed, if you need to reinstall, please delete the %s file first.',
    'Database connection failed:%s'                                                                                      => 'Database connection failure：%s',
    'Failed to install SQL execution:%s'                                                                                 => 'Installation SQL execution failed：%s',
    'unknown'                                                                                                            => 'Unknown',
    'Database does not exist'                                                                                            => 'Database does not exist!',
    'No built front-end file found, please rebuild manually!'                                                            => 'No built front-end file found, please rebuild manually.',
    'Failed to move the front-end file, please move it manually!'                                                        => 'Failed to move the front-end file, please move manually！',
    'How to solve?'                                                                                                      => 'How to solve?',
    'View reason'                                                                                                        => 'View reasons',
    'Click to view the reason'                                                                                           => 'Click to see the reason',
    'PDO extensions need to be installed'                                                                                => 'pdo_mysql extensions need to be installed.',
    'proc_open or proc_close functions in PHP Ini is disabled'                                                           => 'proc_open and proc_close functions in PHP.Ini is disabled.',
    'How to modify'                                                                                                      => 'How to modify?',
    'Click to view how to modify'                                                                                        => 'Click to see how to modify.',
    'Security assurance?'                                                                                                => 'Security assurance?',
    'Using the installation service correctly will not cause any potential security problems. Click to view the details' => 'The correct use of the installation service will not cause any potential security issues. Click to view the details.',
    'Please install NPM first'                                                                                           => 'Please install NPM first.',
    'Installation error:%s'                                                                                              => 'Installation error：%s',
    'Failed to switch package manager. Please modify the configuration file manually:%s'                                 => 'Package manager switch failed, please modify the configuration file manually：%s.',
    'Please upgrade %s version'                                                                                          => 'Please upgrade the %s version',
    'nothing'                                                                                                            => 'Nothing',
    'The gd extension and freeType library need to be installed'                                                         => 'The gd2 extension and freeType library need to be installed',
    'The .env file with database configuration was detected. Please clean up and try again!'                             => 'The .env file with database configuration was detected. Please clean up and try again!',
];