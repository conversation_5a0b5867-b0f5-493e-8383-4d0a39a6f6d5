<?php

namespace app\admin\model;

use think\Model;

/**
 * 收支分类模型
 * @property int    $id          分类ID
 * @property string $name        分类名称
 * @property string $type        分类类型
 * @property string $icon        分类图标
 * @property string $color       分类颜色
 * @property int    $sort        排序权重
 * @property string $status      状态
 * @property int    $create_time 创建时间
 * @property int    $update_time 更新时间
 */
class ExpenseCategory extends Model
{
    protected $name = 'expense_categories';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'sort' => 'integer',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp',
    ];

    // 字段映射
    protected $field = [
        'id', 'name', 'type', 'icon', 'color', 'sort', 'status', 'create_time', 'update_time'
    ];

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data): string
    {
        $typeMap = [
            'income' => '收入',
            'expense' => '支出'
        ];
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data): string
    {
        $statusMap = [
            'enable' => '启用',
            'disable' => '禁用'
        ];
        return $statusMap[$data['status']] ?? $data['status'];
    }

    /**
     * 搜索器：按类型搜索
     */
    public function searchTypeAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按状态搜索
     */
    public function searchStatusAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按名称搜索
     */
    public function searchNameAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 获取启用的分类列表
     */
    public static function getEnabledList(string $type = ''): array
    {
        $query = self::where('status', 'enable')->order('sort', 'asc');
        
        if ($type) {
            $query->where('type', $type);
        }
        
        return $query->select()->toArray();
    }

    /**
     * 获取分类选项（用于下拉选择）
     */
    public static function getOptions(string $type = ''): array
    {
        $list = self::getEnabledList($type);
        $options = [];
        
        foreach ($list as $item) {
            $options[] = [
                'value' => $item['id'],
                'label' => $item['name'],
                'type' => $item['type'],
                'icon' => $item['icon'],
                'color' => $item['color']
            ];
        }
        
        return $options;
    }
}
