<template>
    <!-- 对话框表单 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="60%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    ref="formRef"
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    label-position="right"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.user')"
                                type="select"
                                v-model="baTable.form.items!.user_id"
                                prop="user_id"
                                :placeholder="t('Please select field', { field: t('expense.record.user') })"
                                :data="{
                                    content: userOptions,
                                    key: 'id',
                                    value: 'nickname'
                                }"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.type')"
                                type="radio"
                                v-model="baTable.form.items!.type"
                                prop="type"
                                :data="{
                                    content: {
                                        'income': t('expense.category.income'),
                                        'expense': t('expense.category.expense')
                                    }
                                }"
                                @change="onTypeChange"
                            />
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.category')"
                                type="select"
                                v-model="baTable.form.items!.category_id"
                                prop="category_id"
                                :placeholder="t('Please select field', { field: t('expense.record.category') })"
                                :data="{
                                    content: filteredCategories,
                                    key: 'value',
                                    value: 'label'
                                }"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.amount')"
                                type="number"
                                v-model.number="baTable.form.items!.amount"
                                prop="amount"
                                :placeholder="t('Please input field', { field: t('expense.record.amount') })"
                                :input-attr="{ step: 0.01, min: 0 }"
                            />
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.record_date')"
                                type="date"
                                v-model="baTable.form.items!.record_date"
                                prop="record_date"
                                :placeholder="t('Please select field', { field: t('expense.record.record_date') })"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                :label="t('expense.record.record_time')"
                                type="time"
                                v-model="baTable.form.items!.record_time"
                                prop="record_time"
                                :placeholder="t('Please select field', { field: t('expense.record.record_time') })"
                            />
                        </el-col>
                    </el-row>
                    
                    <FormItem
                        :label="t('expense.record.description')"
                        type="textarea"
                        v-model="baTable.form.items!.description"
                        prop="description"
                        :placeholder="t('Please input field', { field: t('expense.record.description') })"
                        :input-attr="{ rows: 3 }"
                    />
                    
                    <FormItem
                        :label="t('expense.record.tags')"
                        type="array"
                        v-model="baTable.form.items!.tags"
                        prop="tags"
                        :placeholder="t('Please input field', { field: t('expense.record.tags') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm('')">{{ t('Cancel') }}</el-button>
                <el-button
                    v-blur
                    :loading="baTable.form.submitLoading"
                    @click="baTable.onSubmit(formRef)"
                    type="primary"
                >
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { inject, reactive, useTemplateRef, computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type baTableClass from '/@/utils/baTable'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import createAxios from '/@/utils/axios'

defineOptions({
    name: 'expense/record/popupForm',
})

const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass
const config = useConfig()
const { t } = useI18n()

// 用户选项和分类选项
const userOptions = ref([])
const categoryOptions = ref([])

// 根据类型过滤分类
const filteredCategories = computed(() => {
    const type = baTable.form.items?.type
    if (!type) return categoryOptions.value
    return categoryOptions.value.filter((item: any) => item.type === type)
})

// 类型改变时重置分类
const onTypeChange = () => {
    baTable.form.items!.category_id = ''
}

// 监听表单操作变化，加载数据
watch(() => baTable.form.operate, async (newVal) => {
    if (newVal === 'Add' || newVal === 'Edit') {
        try {
            const res = await createAxios({
                url: '/admin/expense.Category/select',
                method: 'get'
            })
            if (res.code === 1) {
                categoryOptions.value = res.data
            }
            
            const userRes = await createAxios({
                url: '/admin/auth.Admin/',
                method: 'get',
                params: { select: 1 }
            })
            if (userRes.code === 1) {
                userOptions.value = userRes.data.list || []
            }
        } catch (error) {
            console.error('加载表单数据失败:', error)
        }
    }
})

const rules = reactive({
    user_id: [
        {
            required: true,
            message: t('Please select field', { field: t('expense.record.user') }),
            trigger: 'change',
        },
    ],
    category_id: [
        {
            required: true,
            message: t('Please select field', { field: t('expense.record.category') }),
            trigger: 'change',
        },
    ],
    type: [
        {
            required: true,
            message: t('Please select field', { field: t('expense.record.type') }),
            trigger: 'change',
        },
    ],
    amount: [
        {
            required: true,
            message: t('Please input field', { field: t('expense.record.amount') }),
            trigger: 'blur',
        },
        {
            type: 'number',
            min: 0.01,
            message: t('expense.record.amount_min'),
            trigger: 'blur',
        },
    ],
    record_date: [
        {
            required: true,
            message: t('Please select field', { field: t('expense.record.record_date') }),
            trigger: 'change',
        },
    ],
})
</script>

<style scoped lang="scss"></style>
