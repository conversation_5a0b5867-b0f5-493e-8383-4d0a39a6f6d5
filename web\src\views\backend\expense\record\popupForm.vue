<template>
    <!-- 对话框表单 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="60%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate === 'Add' ? '添加' : baTable.form.operate === 'Edit' ? '编辑' : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    ref="formRef"
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    label-position="right"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                label="用户"
                                type="select"
                                v-model="baTable.form.items!.user_id"
                                prop="user_id"
                                placeholder="请选择用户"
                                :data="{
                                    content: userOptions,
                                    key: 'id',
                                    value: 'nickname'
                                }"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                label="类型"
                                type="radio"
                                v-model="baTable.form.items!.type"
                                prop="type"
                                :data="{
                                    content: {
                                        'income': '收入',
                                        'expense': '支出'
                                    }
                                }"
                                @change="onTypeChange"
                            />
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                label="分类"
                                type="select"
                                v-model="baTable.form.items!.category_id"
                                prop="category_id"
                                placeholder="请选择分类"
                                :data="{
                                    content: filteredCategories,
                                    key: 'value',
                                    value: 'label'
                                }"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                label="金额"
                                type="number"
                                v-model.number="baTable.form.items!.amount"
                                prop="amount"
                                placeholder="请输入金额"
                                :input-attr="{ step: 0.01, min: 0 }"
                            />
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <FormItem
                                label="记录日期"
                                type="date"
                                v-model="baTable.form.items!.record_date"
                                prop="record_date"
                                placeholder="请选择记录日期"
                            />
                        </el-col>
                        <el-col :span="12">
                            <FormItem
                                label="记录时间"
                                type="time"
                                v-model="baTable.form.items!.record_time"
                                prop="record_time"
                                placeholder="请选择记录时间"
                            />
                        </el-col>
                    </el-row>

                    <FormItem
                        label="备注说明"
                        type="textarea"
                        v-model="baTable.form.items!.description"
                        prop="description"
                        placeholder="请输入备注说明"
                        :input-attr="{ rows: 3 }"
                    />

                    <FormItem
                        label="标签"
                        type="array"
                        v-model="baTable.form.items!.tags"
                        prop="tags"
                        placeholder="请输入标签"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm('')">取消</el-button>
                <el-button
                    v-blur
                    :loading="baTable.form.submitLoading"
                    @click="baTable.onSubmit(formRef)"
                    type="primary"
                >
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? '保存并编辑下一项' : '保存' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { inject, reactive, useTemplateRef, computed, ref, watch } from 'vue'
import type baTableClass from '/@/utils/baTable'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import createAxios from '/@/utils/axios'

defineOptions({
    name: 'expense/record/popupForm',
})

const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass
const config = useConfig()

// 用户选项和分类选项
const userOptions = ref([])
const categoryOptions = ref([])

// 根据类型过滤分类
const filteredCategories = computed(() => {
    const type = baTable.form.items?.type
    if (!type) return categoryOptions.value
    return categoryOptions.value.filter((item: any) => item.type === type)
})

// 类型改变时重置分类
const onTypeChange = () => {
    baTable.form.items!.category_id = ''
}

// 监听表单操作变化，加载数据
watch(() => baTable.form.operate, async (newVal) => {
    if (newVal === 'Add' || newVal === 'Edit') {
        try {
            const res = await createAxios({
                url: '/admin/expense.Category/select',
                method: 'get'
            })
            if (res.code === 1) {
                categoryOptions.value = res.data
            }
            
            const userRes = await createAxios({
                url: '/admin/auth.Admin/',
                method: 'get',
                params: { select: 1 }
            })
            if (userRes.code === 1) {
                userOptions.value = userRes.data.list || []
            }
        } catch (error) {
            console.error('加载表单数据失败:', error)
        }
    }
})

const rules = reactive({
    user_id: [
        {
            required: true,
            message: '请选择用户',
            trigger: 'change',
        },
    ],
    category_id: [
        {
            required: true,
            message: '请选择分类',
            trigger: 'change',
        },
    ],
    type: [
        {
            required: true,
            message: '请选择类型',
            trigger: 'change',
        },
    ],
    amount: [
        {
            required: true,
            message: '请输入金额',
            trigger: 'blur',
        },
        {
            type: 'number',
            min: 0.01,
            message: '金额必须大于0.01',
            trigger: 'blur',
        },
    ],
    record_date: [
        {
            required: true,
            message: '请选择记录日期',
            trigger: 'change',
        },
    ],
})
</script>

<style scoped lang="scss"></style>
