<template>
    <div class="default-main">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>收支统计</span>
                    <div class="filter-controls">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="onDateRangeChange"
                        />
                        <el-button type="primary" @click="refreshData">刷新</el-button>
                        <el-button type="success" @click="exportData">导出</el-button>
                    </div>
                </div>
            </template>

            <!-- 统计卡片 -->
            <div class="stats-overview">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <div class="stat-card income-card">
                            <div class="stat-icon">
                                <i class="fa fa-arrow-up"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-title">总收入</div>
                                <div class="stat-value">¥{{ overviewStats.income.amount }}</div>
                                <div class="stat-count">{{ overviewStats.income.count }}笔</div>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-card expense-card">
                            <div class="stat-icon">
                                <i class="fa fa-arrow-down"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-title">总支出</div>
                                <div class="stat-value">¥{{ overviewStats.expense.amount }}</div>
                                <div class="stat-count">{{ overviewStats.expense.count }}笔</div>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-card balance-card">
                            <div class="stat-icon">
                                <i class="fa fa-balance-scale"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-title">结余</div>
                                <div class="stat-value" :class="{ 'negative': parseFloat(overviewStats.balance) < 0 }">
                                    ¥{{ overviewStats.balance }}
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 图表区域 -->
            <div class="charts-container">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-card>
                            <template #header>
                                <span>分类分布</span>
                            </template>
                            <div ref="categoryChartRef" style="height: 300px;"></div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card>
                            <template #header>
                                <span>月度趋势</span>
                            </template>
                            <div ref="trendChartRef" style="height: 300px;"></div>
                        </el-card>
                    </el-col>
                </el-row>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, useTemplateRef, nextTick } from 'vue'
import * as echarts from 'echarts'
import createAxios from '/@/utils/axios'
import { ElMessage } from 'element-plus'

defineOptions({
    name: 'expense/statistics',
})

// 图表引用
const categoryChartRef = useTemplateRef('categoryChartRef')
const trendChartRef = useTemplateRef('trendChartRef')

// 数据
const dateRange = ref<[string, string]>(['', ''])
const overviewStats = reactive({
    income: { amount: '0.00', count: 0 },
    expense: { amount: '0.00', count: 0 },
    balance: '0.00'
})

let categoryChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null

// 获取概览统计
const getOverviewStats = async () => {
    try {
        const params: any = { user_id: 1 } // 这里应该从当前登录用户获取
        if (dateRange.value[0] && dateRange.value[1]) {
            params.start_date = dateRange.value[0]
            params.end_date = dateRange.value[1]
        }
        
        const res = await createAxios({
            url: '/admin/expense.Statistics/getUserStats',
            method: 'get',
            params
        })
        
        if (res.code === 1) {
            Object.assign(overviewStats, res.data)
        }
    } catch (error) {
        console.error('获取概览统计失败:', error)
    }
}

// 获取分类统计
const getCategoryStats = async () => {
    try {
        const params: any = { user_id: 1 }
        if (dateRange.value[0] && dateRange.value[1]) {
            params.start_date = dateRange.value[0]
            params.end_date = dateRange.value[1]
        }
        
        const res = await createAxios({
            url: '/admin/expense.Statistics/getCategoryStats',
            method: 'get',
            params
        })
        
        if (res.code === 1) {
            renderCategoryChart(res.data)
        }
    } catch (error) {
        console.error('获取分类统计失败:', error)
    }
}

// 获取月度趋势
const getMonthlyTrend = async () => {
    try {
        const res = await createAxios({
            url: '/admin/expense.Statistics/getMonthlyTrend',
            method: 'get',
            params: { 
                user_id: 1,
                year: new Date().getFullYear()
            }
        })
        
        if (res.code === 1) {
            renderTrendChart(res.data)
        }
    } catch (error) {
        console.error('获取月度趋势失败:', error)
    }
}

// 渲染分类饼图
const renderCategoryChart = (data: any[]) => {
    if (!categoryChart) return
    
    const chartData = data.map(item => ({
        name: item.category_name,
        value: parseFloat(item.total_amount),
        itemStyle: { color: item.category_color || '#409EFF' }
    }))
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [
            {
                name: '分类分布',
                type: 'pie',
                radius: '50%',
                data: chartData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    }
    
    categoryChart.setOption(option)
}

// 渲染趋势图
const renderTrendChart = (data: any[]) => {
    if (!trendChart) return
    
    const months = data.map(item => `${item.month}月`)
    const incomeData = data.map(item => parseFloat(item.income.amount))
    const expenseData = data.map(item => parseFloat(item.expense.amount))
    
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['收入', '支出']
        },
        xAxis: {
            type: 'category',
            data: months
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '收入',
                type: 'line',
                data: incomeData,
                itemStyle: { color: '#67C23A' }
            },
            {
                name: '支出',
                type: 'line',
                data: expenseData,
                itemStyle: { color: '#F56C6C' }
            }
        ]
    }
    
    trendChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
    await nextTick()
    
    if (categoryChartRef.value) {
        categoryChart = echarts.init(categoryChartRef.value)
    }
    
    if (trendChartRef.value) {
        trendChart = echarts.init(trendChartRef.value)
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        categoryChart?.resize()
        trendChart?.resize()
    })
}

// 刷新数据
const refreshData = () => {
    getOverviewStats()
    getCategoryStats()
    getMonthlyTrend()
}

// 日期范围变化
const onDateRangeChange = () => {
    refreshData()
}

// 导出数据
const exportData = async () => {
    try {
        const params: any = { user_id: 1 }
        if (dateRange.value[0] && dateRange.value[1]) {
            params.start_date = dateRange.value[0]
            params.end_date = dateRange.value[1]
        }
        
        const res = await createAxios({
            url: '/admin/expense.Statistics/export',
            method: 'get',
            params
        })
        
        if (res.code === 1) {
            // 这里可以实现CSV下载逻辑
            ElMessage.success('导出成功')
        }
    } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
    }
}

onMounted(() => {
    initCharts()
    refreshData()
})
</script>

<style scoped lang="scss">
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
    }
}

.stats-overview {
    margin-bottom: 20px;
    
    .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        border-radius: 8px;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        
        .stat-icon {
            font-size: 32px;
            margin-right: 15px;
            width: 50px;
            text-align: center;
        }
        
        .stat-content {
            flex: 1;
            
            .stat-title {
                font-size: 14px;
                color: #666;
                margin-bottom: 5px;
            }
            
            .stat-value {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 5px;
                
                &.negative {
                    color: #f56c6c;
                }
            }
            
            .stat-count {
                font-size: 12px;
                color: #999;
            }
        }
        
        &.income-card {
            .stat-icon {
                color: #67c23a;
            }
            .stat-value {
                color: #67c23a;
            }
        }
        
        &.expense-card {
            .stat-icon {
                color: #f56c6c;
            }
            .stat-value {
                color: #f56c6c;
            }
        }
        
        &.balance-card {
            .stat-icon {
                color: #409eff;
            }
            .stat-value {
                color: #409eff;
            }
        }
    }
}

.charts-container {
    margin-top: 20px;
}
</style>
