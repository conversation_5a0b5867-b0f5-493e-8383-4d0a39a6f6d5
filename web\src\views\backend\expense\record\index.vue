<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 统计卡片 -->
        <div class="expense-stats-cards" style="margin-bottom: 20px;">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-card class="stats-card income-card">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fa fa-arrow-up"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-title">总收入</div>
                                <div class="stats-value">¥{{ stats.income.amount }}</div>
                                <div class="stats-count">{{ stats.income.count }}笔</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stats-card expense-card">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fa fa-arrow-down"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-title">总支出</div>
                                <div class="stats-value">¥{{ stats.expense.amount }}</div>
                                <div class="stats-count">{{ stats.expense.count }}笔</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stats-card balance-card">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fa fa-balance-scale"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-title">结余</div>
                                <div class="stats-value" :class="{ 'negative': parseFloat(stats.balance) < 0 }">¥{{ stats.balance }}</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stats-card">
                        <div class="stats-content">
                            <el-button type="primary" @click="refreshStats">刷新</el-button>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 表格顶部菜单 -->
        <TableHeader
            :buttons="['refresh', 'add', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="'请输入备注说明进行搜索'"
        />

        <!-- 表格 -->
        <Table />

        <!-- 表单 -->
        <PopupForm />
    </div>
</template>

<script setup lang="ts">
import { provide, onMounted, useTemplateRef, reactive } from 'vue'
import baTableClass from '/@/utils/baTable'
import PopupForm from './popupForm.vue'
import Table from '/@/components/table/index.vue'
import TableHeader from '/@/components/table/header/index.vue'
import { defaultOptButtons } from '/@/components/table'
import { baTableApi } from '/@/api/common'
import createAxios from '/@/utils/axios'

defineOptions({
    name: 'expense/record',
})
const tableRef = useTemplateRef('tableRef')
const optButtons = defaultOptButtons(['edit', 'delete'])

// 统计数据
const stats = reactive({
    income: { amount: '0.00', count: 0 },
    expense: { amount: '0.00', count: 0 },
    balance: '0.00'
})

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/expense.Record/'),
    {
        pk: 'id',
        column: [
            { type: 'selection', align: 'center', operator: false },
            { label: 'ID', prop: 'id', align: 'center', operator: '=', operatorPlaceholder: 'ID', width: 70 },
            {
                label: '用户',
                prop: 'user.nickname',
                align: 'center',
                operator: 'LIKE',
                operatorPlaceholder: '模糊查询',
                width: 120
            },
            {
                label: '分类',
                prop: 'category.name',
                align: 'center',
                operator: 'LIKE',
                operatorPlaceholder: '模糊查询',
                width: 120
            },
            {
                label: '类型',
                prop: 'type',
                align: 'center',
                operator: '=',
                render: 'tag',
                custom: { 'income': 'success', 'expense': 'danger' },
                replaceValue: { 'income': '收入', 'expense': '支出' },
                width: 80
            },
            {
                label: '金额',
                prop: 'amount',
                align: 'center',
                operator: 'RANGE',
                render: 'money',
                width: 120
            },
            {
                label: '备注说明',
                prop: 'description',
                align: 'left',
                operator: 'LIKE',
                operatorPlaceholder: '模糊查询',
                'show-overflow-tooltip': true
            },
            {
                label: '记录日期',
                prop: 'record_date',
                align: 'center',
                operator: 'RANGE',
                render: 'date',
                width: 120
            },
            {
                label: '操作',
                align: 'center',
                width: 100,
                render: 'buttons',
                buttons: optButtons,
                operator: false
            }
        ],
        dblClickNotEditColumn: [undefined],
        defaultOrder: { prop: 'record_date', order: 'desc' }
    },
    {
        defaultItems: {
            user_id: 1, // 这里应该从当前登录用户获取
            type: 'expense',
            record_date: new Date().toISOString().split('T')[0],
            record_time: new Date().toTimeString().split(' ')[0],
            amount: 0,
            description: ''
        },
    }
)

// 刷新统计数据
const refreshStats = async () => {
    try {
        const res = await createAxios({
            url: '/admin/expense.Record/statistics',
            method: 'get',
            params: {
                user_id: 1, // 这里应该从当前登录用户获取
                start_date: '',
                end_date: ''
            }
        })
        if (res.code === 1) {
            Object.assign(stats, res.data)
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()
    baTable.getData()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
    refreshStats()
})
</script>

<style scoped lang="scss">
.expense-stats-cards {
    .stats-card {
        height: 100px;
        
        .stats-content {
            display: flex;
            align-items: center;
            height: 100%;
            
            .stats-icon {
                font-size: 32px;
                margin-right: 15px;
                width: 50px;
                text-align: center;
            }
            
            .stats-info {
                flex: 1;
                
                .stats-title {
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 5px;
                }
                
                .stats-value {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 5px;
                    
                    &.negative {
                        color: #f56c6c;
                    }
                }
                
                .stats-count {
                    font-size: 12px;
                    color: #999;
                }
            }
        }
        
        &.income-card {
            .stats-icon {
                color: #67c23a;
            }
            .stats-value {
                color: #67c23a;
            }
        }
        
        &.expense-card {
            .stats-icon {
                color: #f56c6c;
            }
            .stats-value {
                color: #f56c6c;
            }
        }
        
        &.balance-card {
            .stats-icon {
                color: #409eff;
            }
            .stats-value {
                color: #409eff;
            }
        }
    }
}
</style>
