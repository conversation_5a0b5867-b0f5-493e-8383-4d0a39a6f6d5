export default {
    title: 'Title',
    Icon: 'Icon',
    name: 'Name',
    type: 'Type',
    cache: '<PERSON><PERSON>',
    'Superior menu rule': 'Superior menu rules',
    'Rule type': 'Rule type',
    'type menu_dir': 'Menu directory',
    'type menu': 'Menu item',
    'type button': 'Page button',
    'Rule title': 'Rule title',
    'Rule name': 'Rule name',
    'Routing path': 'Routing path',
    'Rule Icon': 'Rule Icon',
    'Menu type': 'Menu type',
    'Menu type tab': 'Tab',
    'Menu type link (offsite)': 'Link (off-site)',
    'Link address': 'Link address',
    'Component path': 'Component path',
    'Extended properties': 'Extended properties',
    'Add as route only': 'Add as route only',
    'Add as menu only': 'Add as menu only',
    'Rule comments': 'Rule comments',
    'Rule weight': 'Rule weights',
    'Please enter the weight of menu rule (sort by)': 'Please enter the menu rule weights (sort by)',
    'Please enter the correct URL': 'Please enter the correct URL',
    'The superior menu rule cannot be the rule itself': 'The superior menu rules cannot be rules itself.',
    'It will be registered as the web side routing name and used as the server side API authentication':
        'It will be registered as the routing name of the webside and used as a server-side API authentication at the same time.',
    'Please enter the URL address of the link or iframe': 'Please enter the link or the URL address of iframe.',
    'English name, which does not need to start with `/admin`, such as auth/menu':
        'The English name does not need to start with `/admin`, such as: auth/menu.',
    'Web side component path, please start with /src, such as: /src/views/backend/dashboard':
        'Please start with /src for web side component paths, such as: /src/views/backend/dashboard.vue',
    'The web side routing path (path) does not need to start with `/admin`, such as auth/menu':
        'The web side routing path (Path) does not need to start with `/admin`, such as: auth/menu.',
    'Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console':
        'Use the `get_route_remark()` function in the controller can get the value of this field for your own use, such as the banner file for the console.',
    'extend Title':
        "For example, if 'auth/menu' is only added as a route, then `auth/menu`, `auth/menu/:a` and `auth/menu/:b/:c` can be added only as menus.",
    none: 'None',
}
