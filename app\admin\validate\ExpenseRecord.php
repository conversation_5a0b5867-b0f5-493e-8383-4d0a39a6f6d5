<?php

namespace app\admin\validate;

use think\Validate;

class ExpenseRecord extends Validate
{
    protected $failException = true;

    protected $rule = [
        'user_id'     => 'require|integer|>:0',
        'category_id' => 'require|integer|>:0',
        'type'        => 'require|in:income,expense',
        'amount'      => 'require|float|>:0',
        'description' => 'length:0,255',
        'record_date' => 'require|date',
        'record_time' => 'time',
        'tags'        => 'array',
        'attachment'  => 'array',
    ];

    protected $message = [
        'user_id.require'     => '用户ID不能为空',
        'user_id.integer'     => '用户ID必须是整数',
        'user_id.>'           => '用户ID必须大于0',
        'category_id.require' => '分类ID不能为空',
        'category_id.integer' => '分类ID必须是整数',
        'category_id.>'       => '分类ID必须大于0',
        'type.require'        => '记录类型不能为空',
        'type.in'             => '记录类型只能是收入或支出',
        'amount.require'      => '金额不能为空',
        'amount.float'        => '金额必须是数字',
        'amount.>'            => '金额必须大于0',
        'description.length'  => '备注说明长度不能超过255个字符',
        'record_date.require' => '记录日期不能为空',
        'record_date.date'    => '记录日期格式不正确',
        'record_time.time'    => '记录时间格式不正确',
        'tags.array'          => '标签必须是数组格式',
        'attachment.array'    => '附件必须是数组格式',
    ];

    protected $scene = [
        'add'  => ['user_id', 'category_id', 'type', 'amount', 'description', 'record_date', 'record_time', 'tags', 'attachment'],
        'edit' => ['user_id', 'category_id', 'type', 'amount', 'description', 'record_date', 'record_time', 'tags', 'attachment'],
    ];
}
