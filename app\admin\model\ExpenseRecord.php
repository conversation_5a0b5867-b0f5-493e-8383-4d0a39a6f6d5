<?php

namespace app\admin\model;

use think\Model;
use think\model\relation\BelongsTo;

/**
 * 收支记录模型
 * @property int    $id          记录ID
 * @property int    $user_id     用户ID
 * @property int    $category_id 分类ID
 * @property string $type        记录类型
 * @property int    $amount      金额(分)
 * @property string $description 备注说明
 * @property string $record_date 记录日期
 * @property string $record_time 记录时间
 * @property string $tags        标签
 * @property string $attachment  附件
 * @property int    $create_time 创建时间
 * @property int    $update_time 更新时间
 */
class ExpenseRecord extends Model
{
    protected $name = 'expense_records';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['tags', 'attachment'];

    // 类型转换
    protected $type = [
        'user_id' => 'integer',
        'category_id' => 'integer',
        'amount' => 'integer',
        'record_date' => 'date',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp',
        'tags' => 'array',
        'attachment' => 'array',
    ];

    // 字段映射
    protected $field = [
        'id', 'user_id', 'category_id', 'type', 'amount', 'description', 
        'record_date', 'record_time', 'tags', 'attachment', 'create_time', 'update_time'
    ];

    /**
     * 金额获取器 - 转换为元
     */
    public function getAmountAttr($value): string
    {
        return bcdiv($value, 100, 2);
    }

    /**
     * 金额修改器 - 转换为分
     */
    public function setAmountAttr($value): int
    {
        return bcmul($value, 100, 0);
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data): string
    {
        $typeMap = [
            'income' => '收入',
            'expense' => '支出'
        ];
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 关联用户模型
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联分类模型
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'category_id');
    }

    /**
     * 搜索器：按用户搜索
     */
    public function searchUserIdAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：按分类搜索
     */
    public function searchCategoryIdAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('category_id', $value);
        }
    }

    /**
     * 搜索器：按类型搜索
     */
    public function searchTypeAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按日期范围搜索
     */
    public function searchRecordDateAttr($query, $value): void
    {
        if (is_array($value) && count($value) == 2) {
            $query->whereBetween('record_date', $value);
        } elseif ($value !== '') {
            $query->where('record_date', $value);
        }
    }

    /**
     * 搜索器：按备注搜索
     */
    public function searchDescriptionAttr($query, $value): void
    {
        if ($value !== '') {
            $query->where('description', 'like', '%' . $value . '%');
        }
    }

    /**
     * 获取用户收支统计
     */
    public static function getUserStats(int $userId, string $startDate = '', string $endDate = ''): array
    {
        $query = self::where('user_id', $userId);
        
        if ($startDate && $endDate) {
            $query->whereBetween('record_date', [$startDate, $endDate]);
        }
        
        $stats = $query->field([
            'type',
            'SUM(amount) as total_amount',
            'COUNT(*) as count'
        ])->group('type')->select()->toArray();
        
        $result = [
            'income' => ['amount' => 0, 'count' => 0],
            'expense' => ['amount' => 0, 'count' => 0],
            'balance' => 0
        ];
        
        foreach ($stats as $stat) {
            $result[$stat['type']] = [
                'amount' => bcdiv($stat['total_amount'], 100, 2),
                'count' => $stat['count']
            ];
        }
        
        $result['balance'] = bcsub($result['income']['amount'], $result['expense']['amount'], 2);
        
        return $result;
    }
}
