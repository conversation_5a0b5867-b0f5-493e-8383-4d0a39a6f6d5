@use 'sass:map';
@use 'mixins' as *;

// 后台主体窗口左右间距
$main-space: 16px;
$primary-light: #3f6ad8;

// --ba-background
$bg-color: () !default;
$bg-color: map.merge(
    (
        '': #f5f5f5,
        'overlay': #ffffff,
    ),
    $bg-color
);

// --ba-border-color
$border-color: () !default;
$border-color: map.merge(
    (
        '': #f6f6f6,
    ),
    $border-color
);

:root {
    @include set-css-var-value('main-space', $main-space);
    @include set-css-var-value('color-primary-light', $primary-light);
    @include set-component-css-var('bg-color', $bg-color);
    @include set-component-css-var('border-color', $border-color);
}
