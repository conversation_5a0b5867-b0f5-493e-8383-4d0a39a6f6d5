<?php

namespace app\admin\validate;

use think\Validate;

class ExpenseCategory extends Validate
{
    protected $failException = true;

    protected $rule = [
        'name'   => 'require|length:1,50|unique:expense_categories',
        'type'   => 'require|in:income,expense',
        'icon'   => 'length:0,50',
        'color'  => 'length:0,20',
        'sort'   => 'integer|>=:0',
        'status' => 'require|in:enable,disable',
    ];

    protected $message = [
        'name.require'   => '分类名称不能为空',
        'name.length'    => '分类名称长度不能超过50个字符',
        'name.unique'    => '分类名称已存在',
        'type.require'   => '分类类型不能为空',
        'type.in'        => '分类类型只能是收入或支出',
        'icon.length'    => '图标长度不能超过50个字符',
        'color.length'   => '颜色长度不能超过20个字符',
        'sort.integer'   => '排序必须是整数',
        'sort.>='        => '排序不能小于0',
        'status.require' => '状态不能为空',
        'status.in'      => '状态只能是启用或禁用',
    ];

    protected $scene = [
        'add'  => ['name', 'type', 'icon', 'color', 'sort', 'status'],
        'edit' => ['name', 'type', 'icon', 'color', 'sort', 'status'],
    ];

    /**
     * 编辑场景验证规则
     */
    public function sceneEdit(): ExpenseCategory
    {
        return $this->remove('name', 'unique');
    }
}
