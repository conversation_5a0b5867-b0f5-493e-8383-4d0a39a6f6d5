export default {
    'Normal routing': 'Normal routing',
    'Member center menu contents': 'Member center menu directory ',
    'Member center menu items': 'Member Center menu items',
    'Top bar menu items': 'Top bar menu items',
    'Page button': 'Page button',
    'Top bar user dropdown': 'Top bar user dropdown',
    'Type route tips': 'Automatically register as a front-end route',
    'Type menu_dir tips': 'Automatically register routes and serve as menu directory of member center This item cannot jump',
    'Type menu tips': 'Automatically register routes and serve as menu items in member centers',
    'Type nav tips': 'Routes are automatically registered as menu items in the top bar of the site',
    'Type button tips': 'Automatic registration as a permission node, can be quickly verified by v-auth',
    'Type nav_user_menu tips': 'Automatically register routes and serve as a dropdown menu for top bar members',
    'English name': 'English name',
    'Web side routing path': 'Web side routing path',
    no_login_valid: 'no login valid',
    'no_login_valid 0': 'no',
    'no_login_valid 1': 'yes',
    'no_login_valid tips': 'Tourists do not have membership groups Use this option to set whether the current rules are valid for tourists (visible)',
    'For example, if you add account/overview as a route only':
        'Please start with /src for web side component paths, such as: /src/views/frontend/index.vue',
    'Web side component path, please start with /src, such as: /src/views/frontend/index':
        "For example, if you add 'account/overview' as a route only, then you can additionally add 'account/overview', 'account/overview/:a' and 'account/overview/:b/:C' as menus only.",
    'Component path tips':
        'This item is mandatory within a WEB project; otherwise, it cannot be accessed. However, when it is used as a menu within a Nuxt project, there is no need to fill in this item',
}
