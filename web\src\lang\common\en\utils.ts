export default {
    'The moving position is beyond the movable range!': 'The movement position is beyond the removable range!',
    'Navigation failed, the menu type is unrecognized!': 'Navigation failed, menu type not recognized!',
    'Navigation failed, navigation guard intercepted!': 'Navigation failed, Navigation Guard interception!',
    'Navigation failed, it is at the navigation target position!': 'Navigation failed, it is already at the navigation the position!',
    'Navigation failed, invalid route!': 'Navigation failed, invalid route!',
    'No child menu to jump to!': 'No child menu to jump to!',
    Loading: 'Loading...',
    Reload: 'Reload',
    comma: ',',
    'welcome back': 'Welcome back!',
    'Late at night, pay attention to your body!': 'It is late at night. Please tack care of your body!',
    'good morning!': 'Good morning!',
    'Good morning!': 'Good morning!',
    'Good noon!': 'Good noon!',
    'good afternoon': 'Good afternoon.',
    'Good evening': 'Good evening',
    'Hello!': 'Hello!',
    open: 'Open',
    close: 'Close',
    'Clean up system cache': 'Clean up the system cache',
    'Clean up browser cache': 'Clean up browser cache',
    'Clean up all cache': 'Clean up all cache',
    'The data of the uploaded file is incomplete!': 'The data of the uploaded file is incomplete!',
    'The type of uploaded file is not allowed!': 'The type of uploaded file is not allowed!',
    'The size of the uploaded file exceeds the allowed range!': 'The size of the uploaded file exceeds the allowed range!',
    'Please install editor': 'Please install editor',
    // 输入框类型
    mobile: 'Mobile Number',
    'Id number': 'Id Number',
    account: 'Account name',
    password: 'password',
    'variable name': 'Variable Name',
    email: 'Email address',
    date: 'Date',
    number: 'Number',
    float: 'Float',
    integer: 'Integer',
    time: 'Time',
    file: 'File',
    array: 'Array',
    switch: 'Switch',
    year: 'Year',
    image: 'Image',
    select: 'Select',
    string: 'String',
    radio: 'Radio',
    checkbox: 'checkbox',
    'rich Text': 'Rich Text',
    'multi image': 'Multi image',
    textarea: 'Textarea',
    'time date': 'Time Date',
    'remote select': 'Remote Select',
    'city select': 'City select',
    'icon select': 'Icon select',
    'color picker': 'color picker',
    color: 'color',
    choice: ' Choice',
    Icon: 'Icon',
    'Local icon title': 'Local icon:/src/assets/icons Inside.svg',
    'Please select an icon': 'Please select an icon',
    'Ali iconcont Icon': 'Ali Iconfont Icon',
    'Select File': 'Select File',
    'Original name': 'Original name',
    'You can also select': 'You can also select',
    items: 'items',
    Breakdown: 'Detailed catalogue',
    size: 'Size',
    type: 'Type',
    preview: 'Preview',
    'Upload (Reference) times': 'Upload (Reference) times',
    'Last upload time': 'Last upload time',
    'One attribute per line without quotation marks(formitem)':
        'Extensions to FormItem, One attribute per line, no quotation marks required, such as: class=config-item',
    'Extended properties of Input, one line without quotation marks, such as: size=large':
        'Extended properties of Input, one line without quotation marks, such as: size=large',
    'One line at a time, without quotation marks, for example: key1=value1': 'One per line, no quotation marks required, such as: key1=value1',
    Var: 'Var ',
    Name: 'Name',
    Title: 'Title',
    Tip: 'Tip',
    Rule: 'Rule',
    Extend: 'Extend',
    Dict: 'Dict',
    ArrayKey: 'Key',
    ArrayValue: 'Value',
    'No data': 'No data',
}
