export default {
    'stateTitle init': 'Module installer initialization...',
    'stateTitle download': 'Downloading module...',
    'stateTitle install': 'Installing module...',
    'env require': 'Composer',
    'env require-dev': 'Composer-dev',
    'env dependencies': 'NPM',
    'env devDependencies': 'NPM-dev',
    'env nuxtDependencies': 'Nuxt NPM',
    'env nuxtDevDependencies': 'Nuxt NPM Dev',
    // buy
    'Module installation warning':
        'Free download and update within one year after purchase. Virtual products do not support 7-day refund without reason',
    'Order title': 'Order title',
    'Order No': 'Order No.:',
    'Purchase user': 'Purchase user',
    'Order price': 'Order price',
    'Purchased, can be installed directly': 'Purchased, can be installed directly',
    'Understand and agree': 'Understand and agree',
    'Module purchase and use agreement': 'Module purchase and use agreement',
    'Point payment': 'Point payment',
    'Balance payment': 'Balance payment',
    'Wechat payment': 'Wechat payment',
    'Alipay payment': 'Alipay payment',
    'Install now': 'Install now',
    payment: 'payment',
    'Confirm order info': 'Confirm order info',
    // commonDone
    'Congratulations, module installation is complete': 'Congratulations, module installation is complete.',
    'Module is disabled': 'Module is disabled.',
    'Congratulations, the code of the module is ready': 'Congratulations, the code of the module is ready.',
    'Unknown state': 'Unknown state.',
    'Do not refresh the page!': 'Do not refresh the page!',
    'New adjustment of dependency detected': 'New adjustment of dependency detected',
    'This module adds new dependencies': 'This module adds new dependencies',
    'The built-in terminal of the system is automatically installing these dependencies, please wait~':
        'The built-in terminal of the system is automatically installing these dependencies, please wait~',
    'View progress': 'View progress',
    'Dependency installation completed~': 'Dependency installation completed~',
    'This module does not add new dependencies': 'This module does not add new dependencies.',
    'There is no adjustment for system dependency': 'There is no adjustment for system dependency.',
    please: 'please',
    'After installation 1': 'After installation',
    'Manually clean up the system and browser cache': 'Manually clean up the system and browser cache.',
    'After installation 2': 'After installation',
    'Automatically execute reissue command?': 'Automatically execute reissue command?',
    'End of installation': 'End of installation',
    'Dependency installation fail 1': 'The dependency installation failed. Please click the retry button in the ',
    'Dependency installation fail 2': 'terminal',
    'Dependency installation fail 3': 'You can also view the ',
    'Dependency installation fail 4': 'unfinished matters manually',
    'Dependency installation fail 5': 'Until you are',
    'Dependency installation fail 6': 'sure that the dependency is ready',
    'Dependency installation fail 7': ', the module will not work!',
    'Is the command that failed on the WEB terminal executed manually or in other ways successfully?':
        'Is the command that failed on the WEB terminal executed manually or in other ways successfully?',
    yes: 'yes',
    no: 'no',
    // confirmFileConflict
    'Update warning':
        'The following module files have been detected to be updated. When disabled, they will be automatically overwritten. Please pay attention to backup.',
    'File conflict': 'File conflict',
    'Conflict file': 'Conflict file',
    'Dependency conflict': 'Dependency conflict',
    'Confirm to disable the module': 'Confirm to disable the module',
    'The module declares the added dependencies': 'The module declares the added dependencies',
    Dependencies: 'Dependencies',
    retain: 'Retain',
    // goodsInfo
    'detailed information': 'detailed information',
    Price: 'Price',
    'Last updated': 'Last updated',
    'Published on': 'Published on:',
    'amount of downloads': 'amount of downloads',
    'Module classification': 'Module classification',
    'Developer Homepage': 'Developer Homepage',
    'Click to access': 'Click to access',
    'Module status': 'Module status',
    'View demo': 'View demo',
    'Code scanning Preview': 'Code scanning Preview',
    'Buy now': 'Buy now',
    'continue installation': 'continue installation',
    installed: 'installed',
    'to update': 'to update',
    uninstall: 'uninstall',
    'Contact developer': 'Contact developer',
    'Other works of developers': 'Other works of developers',
    'There are no more works': 'There are no more works',
    'You need to disable this module before updating Do you want to disable it now?':
        'You need to disable this module before updating. Do you want to disable it now?',
    'Disable and update': 'Disable and update',
    'No module purchase order was found within the expiration date':
        'No module purchase order was found within the expiration date. Do you want to purchase the current module now?',
    // installConflict
    'new file': 'new file',
    'Existing files': 'Existing files',
    'Treatment scheme': 'Treatment scheme',
    'Backup and overwrite existing files': 'Backup and overwrite existing files',
    'Discard new file': 'Discard new file',
    environment: 'environment',
    'New dependency': 'New dependency',
    'Existing dependencies': 'Existing dependencies',
    'Overwrite existing dependencies': 'Overwrite existing dependencies',
    'Do not use new dependencies': 'Do not use new dependencies',
    // tableHeader
    'Upload zip package for installation': 'Upload zip package for installation',
    'Upload installation': 'Upload installation',
    'Uploaded / installed modules': 'Uploaded / installed modules',
    'Local module': 'Local module',
    'Publishing module': 'Publishing module',
    'Get points': 'Get points',
    'Search is actually very simple': 'Search is actually very simple',
    // tabs
    Loading: 'Loading...',
    'No more': 'No more.',
    // uploadInstall
    'Local upload warning':
        'Please make sure that the module package file comes from the official channel or the officially certified module author, otherwise the system may be damaged because:',
    'The module can modify and add system files': 'The module can modify and add system files',
    'The module can execute sql commands and codes': 'The module can execute sql commands and codes',
    'The module can install new front and rear dependencies': 'The module can install new front and rear dependencies',
    'Drag the module package file here': 'Drag the module package file here, Or',
    'Click me to upload': 'Click me to upload',
    'Uploaded, installation is about to start, please wait': 'Uploaded, installation is about to start, please wait',
    'Update Log': 'Update Log',
    'No detailed update log': 'No detailed update log',
    'Use WeChat to scan QR code for payment': 'Use WeChat to scan QR code for payment',
    'Use Alipay to scan QR code for payment': 'Use Alipay to scan QR code for payment',
    'dependency-installation-fail-tips':
        'If the command is successfully executed manually, click `Make sure dependency is ready` above to change the module to the installed state',
    'New version': 'New version',
    Install: 'Install',
    'Installation cancelled because module already exists!': 'Installation cancelled because module already exists!',
    'Installation cancelled because the directory required by the module is occupied!':
        'Installation cancelled because the directory required by the module is occupied!',
    'Installation complete': 'Installation complete',
    'A conflict is found Please handle it manually': 'A conflict is found. Please handle it manually',
    'Wait for dependent installation': 'Wait for dependent installation',
    'The operation succeeds Please clear the system cache and refresh the browser ~':
        'The operation succeeds. Please clear the system cache and refresh the browser ~',
    'Deal with conflict': 'Deal with conflict',
    'Wait for installation': 'Wait for installation',
    'Conflict pending': 'Conflict pending',
    'Dependency to be installed': 'Dependency to be installed',
    'Restart Vite hot server': 'Restart Vite hot server',
    'Restart Vite hot server tips':
        'Before successfully restarting the service, you can find the button to manually restart the service from the button group on the right side of the top bar.',
    'Manual restart': 'Manual restart',
    'Restart Now': 'Restart Now',
}
