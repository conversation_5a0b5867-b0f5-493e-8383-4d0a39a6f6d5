export default {
    id: 'ID',
    table_name: '数据表名',
    comment: '表注释',
    table: '数据表数据',
    fields: '字段数据',
    sync: '是否上传',
    'sync no': '否',
    'sync yes': '是',
    status: '状态',
    delete: '删除代码',
    'status delete': '代码已删除',
    'status success': '成功',
    'status error': '失败',
    'status start': '生成中',
    create_time: '创建时间',
    'quick Search Fields': 'ID、表名、注释',
    'Upload the selected design records to the cloud for cross-device use': '上传选中的设计记录至云端以跨设备使用',
    'Design records that have been synchronized to the cloud': '已同步至云端的设计记录',
    'Cloud record': '云记录',
    Settings: '设置',
    'Login for backup design': '登录以备份设计',
    'CRUD design record synchronization scheme': 'CRUD 设计记录同步方案',
    Manual: '手动',
    automatic: '自动',
    'When automatically synchronizing records, share them to the open source community': '自动同步记录时分享至开源社区',
    'Not to share': '不分享',
    Share: '分享',
    'Enabling sharing can automatically earn community points during development': '开启分享可于开发同时自动获取社区积分',
    'The synchronized CRUD records are automatically resynchronized when they are updated': '已同步的 CRUD 记录被更新时自动重新同步',
    'Do not resynchronize': '不重新同步',
    'Automatic resynchronization': '自动重新同步',
    'No effective design': '无有效设计',
    'Number of fields': '字段数',
    'Upload type': '上传类型',
    Update: '更新',
    'New added': '新增',
    'Share to earn points': '分享获得积分',
    'Share to the open source community': '分享至开源社区',
    'No design record': '无设计记录',
    Field: '字段',
    'Field information': '字段信息',
    'No field': '无字段',
    'Field name': '字段名',
    Note: '注释',
    Type: '类型',
    Load: '载入',
    'Delete cloud records?': '删除云端记录？',
    'You can use the synchronized design records across devices':
        '您可以跨设备使用已同步的设计记录；选择手动同步时，系统不会主动收集任何数据，同时系统永远不会同步表内数据',
}
