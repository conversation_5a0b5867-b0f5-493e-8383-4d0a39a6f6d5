.block-loading {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 2147483600;
    background-color: var(--ba-bg-color);
}
.block-loading .block-loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.block-loading .block-loading-box-warp {
    width: 80px;
    height: 80px;
}
.block-loading .block-loading-box-warp .block-loading-box-item {
    width: 33.333333%;
    height: 33.333333%;
    background: #409eff;
    float: left;
    animation: block-loading-animation 1.2s infinite ease;
    border-radius: 1px;
}
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(7) {
    animation-delay: 0s;
}
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(4),
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(8) {
    animation-delay: 0.1s;
}
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(1),
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(5),
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(9) {
    animation-delay: 0.2s;
}
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(2),
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(6) {
    animation-delay: 0.3s;
}
.block-loading .block-loading-box-warp .block-loading-box-item:nth-child(3) {
    animation-delay: 0.4s;
}
@keyframes block-loading-animation {
    0%,
    70%,
    100% {
        transform: scale3D(1, 1, 1);
    }
    35% {
        transform: scale3D(0, 0, 1);
    }
}
