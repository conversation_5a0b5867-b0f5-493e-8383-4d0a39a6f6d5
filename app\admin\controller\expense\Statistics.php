<?php

namespace app\admin\controller\expense;

use app\common\controller\Backend;
use app\admin\model\ExpenseRecord;
use app\admin\model\ExpenseCategory;
use think\facade\Db;
use think\Response;
use Throwable;

/**
 * 收支统计分析
 */
class Statistics extends Backend
{
    /**
     * 获取用户收支统计
     */
    public function getUserStats(): void
    {
        $userId = $this->request->param('user_id', 0);
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $stats = ExpenseRecord::getUserStats($userId, $startDate, $endDate);
        
        $this->success('', $stats);
    }

    /**
     * 获取分类统计
     */
    public function getCategoryStats(): void
    {
        $userId = $this->request->param('user_id', 0);
        $type = $this->request->param('type', ''); // income 或 expense
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $query = ExpenseRecord::alias('r')
            ->leftJoin('expense_categories c', 'r.category_id = c.id')
            ->where('r.user_id', $userId)
            ->field([
                'c.id as category_id',
                'c.name as category_name',
                'c.icon as category_icon',
                'c.color as category_color',
                'r.type',
                'SUM(r.amount) as total_amount',
                'COUNT(r.id) as count'
            ])
            ->group('r.category_id');
        
        if ($type) {
            $query->where('r.type', $type);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('r.record_date', [$startDate, $endDate]);
        }
        
        $result = $query->select()->toArray();
        
        // 转换金额单位
        foreach ($result as &$item) {
            $item['total_amount'] = bcdiv($item['total_amount'], 100, 2);
        }
        
        $this->success('', $result);
    }

    /**
     * 获取月度趋势统计
     */
    public function getMonthlyTrend(): void
    {
        $userId = $this->request->param('user_id', 0);
        $year = $this->request->param('year', date('Y'));
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $result = ExpenseRecord::where('user_id', $userId)
            ->whereYear('record_date', $year)
            ->field([
                'MONTH(record_date) as month',
                'type',
                'SUM(amount) as total_amount',
                'COUNT(id) as count'
            ])
            ->group('MONTH(record_date), type')
            ->order('month ASC')
            ->select()
            ->toArray();
        
        // 整理数据格式
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[$i] = [
                'month' => $i,
                'income' => ['amount' => '0.00', 'count' => 0],
                'expense' => ['amount' => '0.00', 'count' => 0]
            ];
        }
        
        foreach ($result as $item) {
            $month = $item['month'];
            $type = $item['type'];
            $months[$month][$type] = [
                'amount' => bcdiv($item['total_amount'], 100, 2),
                'count' => $item['count']
            ];
        }
        
        $this->success('', array_values($months));
    }

    /**
     * 获取日统计（最近30天）
     */
    public function getDailyStats(): void
    {
        $userId = $this->request->param('user_id', 0);
        $days = $this->request->param('days', 30);
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        $result = ExpenseRecord::where('user_id', $userId)
            ->whereBetween('record_date', [$startDate, $endDate])
            ->field([
                'record_date',
                'type',
                'SUM(amount) as total_amount',
                'COUNT(id) as count'
            ])
            ->group('record_date, type')
            ->order('record_date ASC')
            ->select()
            ->toArray();
        
        // 生成日期范围
        $dateRange = [];
        $currentDate = strtotime($startDate);
        $endTimestamp = strtotime($endDate);
        
        while ($currentDate <= $endTimestamp) {
            $date = date('Y-m-d', $currentDate);
            $dateRange[$date] = [
                'date' => $date,
                'income' => ['amount' => '0.00', 'count' => 0],
                'expense' => ['amount' => '0.00', 'count' => 0]
            ];
            $currentDate = strtotime('+1 day', $currentDate);
        }
        
        // 填充数据
        foreach ($result as $item) {
            $date = $item['record_date'];
            $type = $item['type'];
            if (isset($dateRange[$date])) {
                $dateRange[$date][$type] = [
                    'amount' => bcdiv($item['total_amount'], 100, 2),
                    'count' => $item['count']
                ];
            }
        }
        
        $this->success('', array_values($dateRange));
    }

    /**
     * 导出收支记录
     */
    public function export(): void
    {
        $userId = $this->request->param('user_id', 0);
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        $type = $this->request->param('type', '');
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $query = ExpenseRecord::alias('r')
            ->leftJoin('expense_categories c', 'r.category_id = c.id')
            ->leftJoin('user u', 'r.user_id = u.id')
            ->where('r.user_id', $userId)
            ->field([
                'r.id',
                'u.nickname as user_name',
                'c.name as category_name',
                'r.type',
                'r.amount',
                'r.description',
                'r.record_date',
                'r.record_time',
                'r.create_time'
            ])
            ->order('r.record_date DESC, r.id DESC');
        
        if ($type) {
            $query->where('r.type', $type);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('r.record_date', [$startDate, $endDate]);
        }
        
        $records = $query->select()->toArray();
        
        // 转换数据格式
        $exportData = [];
        $exportData[] = ['ID', '用户', '分类', '类型', '金额', '备注', '记录日期', '记录时间', '创建时间'];
        
        foreach ($records as $record) {
            $exportData[] = [
                $record['id'],
                $record['user_name'],
                $record['category_name'],
                $record['type'] == 'income' ? '收入' : '支出',
                bcdiv($record['amount'], 100, 2),
                $record['description'],
                $record['record_date'],
                $record['record_time'],
                date('Y-m-d H:i:s', $record['create_time'])
            ];
        }
        
        $this->success('', [
            'data' => $exportData,
            'filename' => '收支记录_' . date('YmdHis') . '.csv'
        ]);
    }
}
