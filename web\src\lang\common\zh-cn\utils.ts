export default {
    'The moving position is beyond the movable range!': '移动位置超出了可移动范围！',
    'Navigation failed, the menu type is unrecognized!': '导航失败，菜单类型无法识别！',
    'Navigation failed, navigation guard intercepted!': '导航失败，导航守卫拦截！',
    'Navigation failed, it is at the navigation target position!': '导航失败，已在导航目标位置！',
    'Navigation failed, invalid route!': '导航失败，路由无效！',
    'No child menu to jump to!': '没有找到可以跳转的子级菜单！',
    Loading: '加载中...',
    Reload: '重新加载',
    comma: '，',
    'welcome back': '欢迎回来！',
    'Late at night, pay attention to your body!': '夜深了，注意身体哦！',
    'good morning!': '早上好！',
    'Good morning!': '上午好！',
    'Good noon!': '中午好！',
    'good afternoon': '下午好！',
    'Good evening': '晚上好！',
    'Hello!': '您好！',
    open: '开启',
    close: '关闭',
    'Clean up system cache': '清理系统缓存',
    'Clean up browser cache': '清理浏览器缓存',
    'Clean up all cache': '一键清理所有',
    'The data of the uploaded file is incomplete!': '上传文件的资料不完整！',
    'The type of uploaded file is not allowed!': '上传文件的类型不被允许！',
    'The size of the uploaded file exceeds the allowed range!': '上传文件的大小超出允许范围！',
    'Please install editor': '请先于模块市场安装富文本编辑器。',
    // 输入框类型
    mobile: '手机号',
    'Id number': '身份证号',
    account: '账户名',
    password: '密码',
    'variable name': '变量名',
    email: '邮箱地址',
    date: '日期',
    number: '数字',
    float: '浮点数',
    integer: '整数',
    time: '时间',
    file: '文件',
    array: '数组',
    switch: '开关',
    year: '年份',
    image: '图片',
    select: '下拉框',
    string: '字符串',
    radio: '单选框',
    checkbox: '复选框',
    'rich Text': '富文本',
    'multi image': '多图',
    textarea: '多行文本框',
    'time date': '时间日期',
    'remote select': '远程下拉',
    'city select': '城市选择',
    'icon select': '图标选择',
    'color picker': '颜色选择器',
    color: '颜色',
    choice: '选择',
    Icon: '图标',
    'Local icon title': '本地图标:/src/assets/icons中的.svg',
    'Please select an icon': '请选择图标',
    'Ali iconcont Icon': '阿里 Iconfont 图标',
    'Select File': '选择文件',
    'Original name': '原始名称',
    'You can also select': '还可以选择',
    items: '项',
    Breakdown: '细目',
    size: '大小',
    type: '类型',
    preview: '预览',
    'Upload (Reference) times': '上传(引用)次数',
    'Last upload time': '最后上传时间',
    'One attribute per line without quotation marks(formitem)': 'FormItem 的扩展属性，一行一个，无需引号，比如：class=config-item',
    'Extended properties of Input, one line without quotation marks, such as: size=large': 'Input 的扩展属性，一行一个，无需引号，比如：size=large',
    'One line at a time, without quotation marks, for example: key1=value1': '一行一个，无需引号，比如：key1=value1',
    Var: '变量',
    Name: '名',
    Title: '标题',
    Tip: '提示信息',
    Rule: '验证规则',
    Extend: '扩展属性',
    Dict: '字典数据',
    ArrayKey: '键名',
    ArrayValue: '键值',
    'No data': '无数据',
}
