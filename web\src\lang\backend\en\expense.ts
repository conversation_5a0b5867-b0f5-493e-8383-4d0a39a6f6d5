export default {
    expense: {
        category: {
            name: 'Category Name',
            type: 'Category Type',
            icon: 'Category Icon',
            color: 'Category Color',
            sort: 'Sort',
            income: 'Income',
            expense: 'Expense',
        },
        record: {
            user: 'User',
            category: 'Category',
            type: 'Type',
            amount: 'Amount',
            description: 'Description',
            record_date: 'Record Date',
            record_time: 'Record Time',
            tags: 'Tags',
            attachment: 'Attachment',
            total_income: 'Total Income',
            total_expense: 'Total Expense',
            balance: 'Balance',
            records: 'Records',
            amount_min: 'Amount must be greater than 0.01',
        },
        statistics: {
            title: 'Expense Statistics',
            category_distribution: 'Category Distribution',
            monthly_trend: 'Monthly Trend',
            daily_trend: 'Daily Trend',
            export_success: 'Export Success',
            export_failed: 'Export Failed',
        }
    }
}
