export default {
    // 分类管理
    'expense.category.name': 'Category Name',
    'expense.category.type': 'Category Type',
    'expense.category.icon': 'Category Icon',
    'expense.category.color': 'Category Color',
    'expense.category.sort': 'Sort Order',
    'expense.category.income': 'Income',
    'expense.category.expense': 'Expense',
    'expense.category.management': 'Category Management',
    'expense.category.add_category': 'Add Category',
    'expense.category.edit_category': 'Edit Category',
    'expense.category.delete_category': 'Delete Category',
    'expense.category.category_list': 'Category List',
    'expense.category.default_categories': 'Default Categories',
    'expense.category.custom_category': 'Custom Category',

    // 记录管理
    'expense.record.user': 'User',
    'expense.record.category': 'Category',
    'expense.record.type': 'Type',
    'expense.record.amount': 'Amount',
    'expense.record.description': 'Description',
    'expense.record.record_date': 'Record Date',
    'expense.record.record_time': 'Record Time',
    'expense.record.tags': 'Tags',
    'expense.record.attachment': 'Attachment',
    'expense.record.total_income': 'Total Income',
    'expense.record.total_expense': 'Total Expense',
    'expense.record.balance': 'Balance',
    'expense.record.records': 'Records',
    'expense.record.amount_min': 'Amount must be greater than 0.01',
    'expense.record.management': 'Record Management',
    'expense.record.add_record': 'Add Record',
    'expense.record.edit_record': 'Edit Record',
    'expense.record.delete_record': 'Delete Record',
    'expense.record.record_list': 'Record List',
    'expense.record.income_record': 'Income Record',
    'expense.record.expense_record': 'Expense Record',
    'expense.record.recent_records': 'Recent Records',
    'expense.record.search_records': 'Search Records',
    'expense.record.filter_by_date': 'Filter by Date',
    'expense.record.filter_by_category': 'Filter by Category',
    'expense.record.filter_by_type': 'Filter by Type',
    'expense.record.no_records': 'No records found',

    // 统计分析
    'expense.statistics.title': 'Expense Statistics',
    'expense.statistics.overview': 'Overview',
    'expense.statistics.category_distribution': 'Category Distribution',
    'expense.statistics.monthly_trend': 'Monthly Trend',
    'expense.statistics.daily_trend': 'Daily Trend',
    'expense.statistics.yearly_trend': 'Yearly Trend',
    'expense.statistics.income_trend': 'Income Trend',
    'expense.statistics.expense_trend': 'Expense Trend',
    'expense.statistics.balance_trend': 'Balance Trend',
    'expense.statistics.top_categories': 'Top Categories',
    'expense.statistics.recent_analysis': 'Recent Analysis',
    'expense.statistics.export_data': 'Export Data',
    'expense.statistics.export_success': 'Export Success',
    'expense.statistics.export_failed': 'Export Failed',
    'expense.statistics.date_range': 'Date Range',
    'expense.statistics.start_date': 'Start Date',
    'expense.statistics.end_date': 'End Date',
    'expense.statistics.this_month': 'This Month',
    'expense.statistics.last_month': 'Last Month',
    'expense.statistics.this_year': 'This Year',
    'expense.statistics.last_year': 'Last Year',
    'expense.statistics.custom_range': 'Custom Range',

    // 通用
    'expense.common.expense_management': 'Expense Management',
    'expense.common.financial_management': 'Financial Management',
    'expense.common.money_management': 'Money Management',
    'expense.common.income_and_expense': 'Income & Expense',
    'expense.common.daily_expense': 'Daily Expense',
    'expense.common.personal_finance': 'Personal Finance',
    'expense.common.budget_management': 'Budget Management',
    'expense.common.financial_analysis': 'Financial Analysis',
    'expense.common.cash_flow': 'Cash Flow',
    'expense.common.financial_report': 'Financial Report',
    'expense.common.currency_symbol': '$',
    'expense.common.amount_format': 'Amount Format',
    'expense.common.date_format': 'Date Format',
    'expense.common.time_format': 'Time Format',

    // 消息提示
    'expense.messages.add_success': 'Added successfully',
    'expense.messages.edit_success': 'Updated successfully',
    'expense.messages.delete_success': 'Deleted successfully',
    'expense.messages.add_failed': 'Failed to add',
    'expense.messages.edit_failed': 'Failed to update',
    'expense.messages.delete_failed': 'Failed to delete',
    'expense.messages.load_failed': 'Failed to load data',
    'expense.messages.save_success': 'Saved successfully',
    'expense.messages.save_failed': 'Failed to save',
    'expense.messages.operation_success': 'Operation successful',
    'expense.messages.operation_failed': 'Operation failed',
    'expense.messages.confirm_delete': 'Are you sure you want to delete this item?',
    'expense.messages.confirm_batch_delete': 'Are you sure you want to delete selected items?',
    'expense.messages.no_data': 'No data available',
    'expense.messages.loading': 'Loading...',
    'expense.messages.processing': 'Processing...',

    // 验证消息
    'expense.validation.required_field': 'This field is required',
    'expense.validation.invalid_amount': 'Please enter a valid amount',
    'expense.validation.invalid_date': 'Please select a valid date',
    'expense.validation.invalid_time': 'Please select a valid time',
    'expense.validation.amount_too_small': 'Amount must be greater than 0',
    'expense.validation.amount_too_large': 'Amount is too large',
    'expense.validation.description_too_long': 'Description is too long',
    'expense.validation.invalid_category': 'Please select a valid category',
    'expense.validation.invalid_type': 'Please select a valid type',
}
