export default {
    expense: {
        // 分类管理
        category: {
            name: 'Category Name',
            type: 'Category Type',
            icon: 'Category Icon',
            color: 'Category Color',
            sort: 'Sort Order',
            income: 'Income',
            expense: 'Expense',
            management: 'Category Management',
            add_category: 'Add Category',
            edit_category: 'Edit Category',
            delete_category: 'Delete Category',
            category_list: 'Category List',
            default_categories: 'Default Categories',
            custom_category: 'Custom Category',
        },
        // 记录管理
        record: {
            user: 'User',
            category: 'Category',
            type: 'Type',
            amount: 'Amount',
            description: 'Description',
            record_date: 'Record Date',
            record_time: 'Record Time',
            tags: 'Tags',
            attachment: 'Attachment',
            total_income: 'Total Income',
            total_expense: 'Total Expense',
            balance: 'Balance',
            records: 'Records',
            amount_min: 'Amount must be greater than 0.01',
            management: 'Record Management',
            add_record: 'Add Record',
            edit_record: 'Edit Record',
            delete_record: 'Delete Record',
            record_list: 'Record List',
            income_record: 'Income Record',
            expense_record: 'Expense Record',
            recent_records: 'Recent Records',
            search_records: 'Search Records',
            filter_by_date: 'Filter by Date',
            filter_by_category: 'Filter by Category',
            filter_by_type: 'Filter by Type',
            no_records: 'No records found',
        },
        // 统计分析
        statistics: {
            title: 'Expense Statistics',
            overview: 'Overview',
            category_distribution: 'Category Distribution',
            monthly_trend: 'Monthly Trend',
            daily_trend: 'Daily Trend',
            yearly_trend: 'Yearly Trend',
            income_trend: 'Income Trend',
            expense_trend: 'Expense Trend',
            balance_trend: 'Balance Trend',
            top_categories: 'Top Categories',
            recent_analysis: 'Recent Analysis',
            export_data: 'Export Data',
            export_success: 'Export Success',
            export_failed: 'Export Failed',
            date_range: 'Date Range',
            start_date: 'Start Date',
            end_date: 'End Date',
            this_month: 'This Month',
            last_month: 'Last Month',
            this_year: 'This Year',
            last_year: 'Last Year',
            custom_range: 'Custom Range',
        },
        // 通用
        common: {
            expense_management: 'Expense Management',
            financial_management: 'Financial Management',
            money_management: 'Money Management',
            income_and_expense: 'Income & Expense',
            daily_expense: 'Daily Expense',
            personal_finance: 'Personal Finance',
            budget_management: 'Budget Management',
            financial_analysis: 'Financial Analysis',
            cash_flow: 'Cash Flow',
            financial_report: 'Financial Report',
            currency_symbol: '$',
            amount_format: 'Amount Format',
            date_format: 'Date Format',
            time_format: 'Time Format',
        },
        // 消息提示
        messages: {
            add_success: 'Added successfully',
            edit_success: 'Updated successfully',
            delete_success: 'Deleted successfully',
            add_failed: 'Failed to add',
            edit_failed: 'Failed to update',
            delete_failed: 'Failed to delete',
            load_failed: 'Failed to load data',
            save_success: 'Saved successfully',
            save_failed: 'Failed to save',
            operation_success: 'Operation successful',
            operation_failed: 'Operation failed',
            confirm_delete: 'Are you sure you want to delete this item?',
            confirm_batch_delete: 'Are you sure you want to delete selected items?',
            no_data: 'No data available',
            loading: 'Loading...',
            processing: 'Processing...',
        },
        // 验证消息
        validation: {
            required_field: 'This field is required',
            invalid_amount: 'Please enter a valid amount',
            invalid_date: 'Please select a valid date',
            invalid_time: 'Please select a valid time',
            amount_too_small: 'Amount must be greater than 0',
            amount_too_large: 'Amount is too large',
            description_too_long: 'Description is too long',
            invalid_category: 'Please select a valid category',
            invalid_type: 'Please select a valid type',
        },
    },
}
