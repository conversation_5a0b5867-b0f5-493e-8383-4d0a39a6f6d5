<?php

use think\migration\Migrator;
use think\facade\Db;

class InitExpenseCategoriesData extends Migrator
{
    /**
     * 初始化收支分类数据
     */
    public function up(): void
    {
        $nowTime = time();
        
        // 收入分类
        $incomeCategories = [
            ['name' => '工资收入', 'type' => 'income', 'icon' => 'fa fa-money', 'color' => '#67C23A', 'sort' => 1],
            ['name' => '奖金', 'type' => 'income', 'icon' => 'fa fa-trophy', 'color' => '#E6A23C', 'sort' => 2],
            ['name' => '投资收益', 'type' => 'income', 'icon' => 'fa fa-line-chart', 'color' => '#409EFF', 'sort' => 3],
            ['name' => '兼职收入', 'type' => 'income', 'icon' => 'fa fa-briefcase', 'color' => '#909399', 'sort' => 4],
            ['name' => '礼金红包', 'type' => 'income', 'icon' => 'fa fa-gift', 'color' => '#F56C6C', 'sort' => 5],
            ['name' => '其他收入', 'type' => 'income', 'icon' => 'fa fa-plus-circle', 'color' => '#606266', 'sort' => 99],
        ];

        // 支出分类
        $expenseCategories = [
            ['name' => '餐饮美食', 'type' => 'expense', 'icon' => 'fa fa-cutlery', 'color' => '#F56C6C', 'sort' => 1],
            ['name' => '交通出行', 'type' => 'expense', 'icon' => 'fa fa-car', 'color' => '#409EFF', 'sort' => 2],
            ['name' => '购物消费', 'type' => 'expense', 'icon' => 'fa fa-shopping-cart', 'color' => '#E6A23C', 'sort' => 3],
            ['name' => '生活缴费', 'type' => 'expense', 'icon' => 'fa fa-home', 'color' => '#67C23A', 'sort' => 4],
            ['name' => '医疗健康', 'type' => 'expense', 'icon' => 'fa fa-heartbeat', 'color' => '#F56C6C', 'sort' => 5],
            ['name' => '学习教育', 'type' => 'expense', 'icon' => 'fa fa-book', 'color' => '#909399', 'sort' => 6],
            ['name' => '娱乐休闲', 'type' => 'expense', 'icon' => 'fa fa-gamepad', 'color' => '#E6A23C', 'sort' => 7],
            ['name' => '服装美容', 'type' => 'expense', 'icon' => 'fa fa-female', 'color' => '#F56C6C', 'sort' => 8],
            ['name' => '人情往来', 'type' => 'expense', 'icon' => 'fa fa-users', 'color' => '#67C23A', 'sort' => 9],
            ['name' => '投资理财', 'type' => 'expense', 'icon' => 'fa fa-bank', 'color' => '#409EFF', 'sort' => 10],
            ['name' => '其他支出', 'type' => 'expense', 'icon' => 'fa fa-minus-circle', 'color' => '#606266', 'sort' => 99],
        ];

        // 合并所有分类并添加时间戳
        $allCategories = array_merge($incomeCategories, $expenseCategories);
        foreach ($allCategories as &$category) {
            $category['status'] = 'enable';
            $category['create_time'] = $nowTime;
            $category['update_time'] = $nowTime;
        }

        // 插入数据
        $table = $this->table('expense_categories');
        $table->insert($allCategories)->saveData();
    }

    /**
     * 回滚操作
     */
    public function down(): void
    {
        $this->execute('DELETE FROM expense_categories');
    }
}
