<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 表格顶部菜单 -->
        <TableHeader
            :buttons="['refresh', 'add', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="'请输入分类名称进行搜索'"
        />

        <!-- 表格 -->
        <Table />

        <!-- 表单 -->
        <PopupForm />
    </div>
</template>

<script setup lang="ts">
import { provide, onMounted, useTemplateRef } from 'vue'
import baTableClass from '/@/utils/baTable'
import PopupForm from './popupForm.vue'
import Table from '/@/components/table/index.vue'
import TableHeader from '/@/components/table/header/index.vue'
import { defaultOptButtons } from '/@/components/table'
import { baTableApi } from '/@/api/common'
defineOptions({
    name: 'expense/category',
})
const tableRef = useTemplateRef('tableRef')
const optButtons = defaultOptButtons(['edit', 'delete'])

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/expense.Category/'),
    {
        pk: 'id',
        column: [
            { type: 'selection', align: 'center', operator: false },
            { label: 'ID', prop: 'id', align: 'center', operator: '=', operatorPlaceholder: 'ID', width: 70 },
            {
                label: '分类名称',
                prop: 'name',
                align: 'center',
                operator: 'LIKE',
                operatorPlaceholder: '模糊查询'
            },
            {
                label: '分类类型',
                prop: 'type',
                align: 'center',
                operator: '=',
                render: 'tag',
                custom: { 'income': 'success', 'expense': 'danger' },
                replaceValue: { 'income': '收入', 'expense': '支出' }
            },
            {
                label: '分类图标',
                prop: 'icon',
                align: 'center',
                operator: false,
                render: 'icon'
            },
            {
                label: '分类颜色',
                prop: 'color',
                align: 'center',
                operator: false,
                render: 'color'
            },
            {
                label: '排序权重',
                prop: 'sort',
                align: 'center',
                operator: '=',
                width: 80
            },
            {
                label: '状态',
                prop: 'status',
                align: 'center',
                render: 'tag',
                operator: '=',
                custom: { 'enable': 'success', 'disable': 'danger' },
                replaceValue: { 'enable': '启用', 'disable': '禁用' }
            },
            {
                label: '更新时间',
                prop: 'update_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                width: 160
            },
            {
                label: '操作',
                align: 'center',
                width: 100,
                render: 'buttons',
                buttons: optButtons,
                operator: false
            }
        ],
        dblClickNotEditColumn: [undefined],
        defaultOrder: { prop: 'sort', order: 'asc' }
    },
    {
        defaultItems: {
            type: 'expense',
            status: 'enable',
            sort: 0,
            icon: 'fa fa-circle',
            color: '#409EFF'
        },
    }
)

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()
    baTable.getData()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
})
</script>

<style scoped lang="scss"></style>
