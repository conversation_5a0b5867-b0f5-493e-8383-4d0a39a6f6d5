<template>
    <!-- 对话框表单 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="50%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    ref="formRef"
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    label-position="right"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('expense.category.name')"
                        type="string"
                        v-model="baTable.form.items!.name"
                        prop="name"
                        :placeholder="t('Please input field', { field: t('expense.category.name') })"
                    />
                    <FormItem
                        :label="t('expense.category.type')"
                        type="radio"
                        v-model="baTable.form.items!.type"
                        prop="type"
                        :data="{
                            content: {
                                'income': t('expense.category.income'),
                                'expense': t('expense.category.expense')
                            }
                        }"
                    />
                    <FormItem
                        :label="t('expense.category.icon')"
                        type="icon"
                        v-model="baTable.form.items!.icon"
                        prop="icon"
                        :placeholder="t('Please select field', { field: t('expense.category.icon') })"
                    />
                    <FormItem
                        :label="t('expense.category.color')"
                        type="color"
                        v-model="baTable.form.items!.color"
                        prop="color"
                    />
                    <FormItem
                        :label="t('expense.category.sort')"
                        type="number"
                        v-model.number="baTable.form.items!.sort"
                        prop="sort"
                        :placeholder="t('Please input field', { field: t('expense.category.sort') })"
                    />
                    <FormItem
                        :label="t('State')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :data="{
                            content: {
                                'enable': t('Enable'),
                                'disable': t('Disable')
                            }
                        }"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm('')">{{ t('Cancel') }}</el-button>
                <el-button
                    v-blur
                    :loading="baTable.form.submitLoading"
                    @click="baTable.onSubmit(formRef)"
                    type="primary"
                >
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import type baTableClass from '/@/utils/baTable'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'

defineOptions({
    name: 'expense/category/popupForm',
})

const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass
const config = useConfig()
const { t } = useI18n()

const rules = reactive({
    name: [
        {
            required: true,
            message: t('Please input field', { field: t('expense.category.name') }),
            trigger: 'blur',
        },
    ],
    type: [
        {
            required: true,
            message: t('Please select field', { field: t('expense.category.type') }),
            trigger: 'change',
        },
    ],
    status: [
        {
            required: true,
            message: t('Please select field', { field: t('State') }),
            trigger: 'change',
        },
    ],
})
</script>

<style scoped lang="scss"></style>
