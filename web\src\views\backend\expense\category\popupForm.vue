<template>
    <!-- 对话框表单 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="50%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate === 'Add' ? '添加' : baTable.form.operate === 'Edit' ? '编辑' : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    ref="formRef"
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    label-position="right"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        label="分类名称"
                        type="string"
                        v-model="baTable.form.items!.name"
                        prop="name"
                        placeholder="请输入分类名称"
                    />
                    <FormItem
                        label="分类类型"
                        type="radio"
                        v-model="baTable.form.items!.type"
                        prop="type"
                        :data="{
                            content: {
                                'income': '收入',
                                'expense': '支出'
                            }
                        }"
                    />
                    <FormItem
                        label="分类图标"
                        type="icon"
                        v-model="baTable.form.items!.icon"
                        prop="icon"
                        placeholder="请选择分类图标"
                    />
                    <FormItem
                        label="分类颜色"
                        type="color"
                        v-model="baTable.form.items!.color"
                        prop="color"
                    />
                    <FormItem
                        label="排序权重"
                        type="number"
                        v-model.number="baTable.form.items!.sort"
                        prop="sort"
                        placeholder="请输入排序权重"
                    />
                    <FormItem
                        label="状态"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :data="{
                            content: {
                                'enable': '启用',
                                'disable': '禁用'
                            }
                        }"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm('')">取消</el-button>
                <el-button
                    v-blur
                    :loading="baTable.form.submitLoading"
                    @click="baTable.onSubmit(formRef)"
                    type="primary"
                >
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? '保存并编辑下一项' : '保存' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { inject, reactive, useTemplateRef } from 'vue'
import type baTableClass from '/@/utils/baTable'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'

defineOptions({
    name: 'expense/category/popupForm',
})

const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass
const config = useConfig()

const rules = reactive({
    name: [
        {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
        },
    ],
    type: [
        {
            required: true,
            message: '请选择分类类型',
            trigger: 'change',
        },
    ],
    status: [
        {
            required: true,
            message: '请选择状态',
            trigger: 'change',
        },
    ],
})
</script>

<style scoped lang="scss"></style>
