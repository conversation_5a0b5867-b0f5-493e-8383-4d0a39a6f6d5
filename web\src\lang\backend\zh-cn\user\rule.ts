export default {
    'Normal routing': '普通路由',
    'Member center menu contents': '会员中心菜单目录',
    'Member center menu items': '会员中心菜单项',
    'Top bar menu items': '顶栏菜单项',
    'Page button': '页面按钮',
    'Top bar user dropdown': '顶栏会员菜单下拉项',
    'Type route tips': '自动注册为前端路由',
    'Type menu_dir tips': '自动注册路由，并作为会员中心的菜单目录，此项本身不可跳转',
    'Type menu tips': '自动注册路由，并作为会员中心的菜单项目',
    'Type nav tips': '自动注册路由，并作为站点顶栏的菜单项目',
    'Type button tips': '自动注册为权限节点，可通过 v-auth 快速验权',
    'Type nav_user_menu tips': '自动注册路由，并作为顶栏会员菜单下拉项',
    'English name': '英文名称',
    'Web side routing path': 'WEB 端路由路径（vue-router 的 path）',
    no_login_valid: '未登录有效',
    'no_login_valid 0': '游客无效',
    'no_login_valid 1': '游客有效',
    'no_login_valid tips': '游客没有会员分组，通过本选项设置当前规则是否对游客有效（可见）',
    'For example, if you add account/overview as a route only': 'WEB 端组件路径，请以 /src 开头，如：/src/views/frontend/index.vue',
    'Web side component path, please start with /src, such as: /src/views/frontend/index':
        '比如将 `account/overview` 只添加为路由，那么可以另外将 `account/overview`、`account/overview/:a`、`account/overview/:b/:c` 只添加为菜单',
    'Component path tips': '组件路径在 WEB 工程内是必填的，否则无法访问，但作为 Nuxt 工程内的菜单时，无需填写此项，请根据菜单使用场景填写',
}
