<?php

use think\migration\Migrator;
use think\facade\Db;

class AddExpenseMenuRules extends Migrator
{
    /**
     * 添加收支管理菜单和权限
     */
    public function up(): void
    {
        $nowTime = time();
        
        // 插入主菜单
        $expenseMenuId = Db::name('admin_rule')->insertGetId([
            'pid' => 0,
            'type' => 'menu_dir',
            'title' => '收支管理',
            'name' => 'expense',
            'path' => 'expense',
            'icon' => 'fa fa-money',
            'menu_type' => 'tab',
            'extend' => 'none',
            'remark' => '日常收支管理模块',
            'weigh' => 90,
            'status' => 'normal',
            'update_time' => $nowTime,
            'create_time' => $nowTime,
        ]);

        // 收支分类管理
        $categoryMenuId = Db::name('admin_rule')->insertGetId([
            'pid' => $expenseMenuId,
            'type' => 'menu',
            'title' => '分类管理',
            'name' => 'expense/category',
            'path' => 'expense/category',
            'icon' => 'fa fa-tags',
            'menu_type' => 'tab',
            'extend' => 'none',
            'remark' => '收支分类管理',
            'weigh' => 1,
            'status' => 'normal',
            'update_time' => $nowTime,
            'create_time' => $nowTime,
        ]);

        // 分类管理权限节点
        $categoryButtons = [
            ['name' => 'expense/category/index', 'title' => '查看', 'type' => 'button'],
            ['name' => 'expense/category/add', 'title' => '添加', 'type' => 'button'],
            ['name' => 'expense/category/edit', 'title' => '编辑', 'type' => 'button'],
            ['name' => 'expense/category/del', 'title' => '删除', 'type' => 'button'],
        ];

        foreach ($categoryButtons as $button) {
            Db::name('admin_rule')->insert([
                'pid' => $categoryMenuId,
                'type' => $button['type'],
                'title' => $button['title'],
                'name' => $button['name'],
                'path' => '',
                'icon' => '',
                'menu_type' => '',
                'extend' => 'none',
                'remark' => '',
                'weigh' => 0,
                'status' => 'normal',
                'update_time' => $nowTime,
                'create_time' => $nowTime,
            ]);
        }

        // 收支记录管理
        $recordMenuId = Db::name('admin_rule')->insertGetId([
            'pid' => $expenseMenuId,
            'type' => 'menu',
            'title' => '记录管理',
            'name' => 'expense/record',
            'path' => 'expense/record',
            'icon' => 'fa fa-list-alt',
            'menu_type' => 'tab',
            'extend' => 'none',
            'remark' => '收支记录管理',
            'weigh' => 2,
            'status' => 'normal',
            'update_time' => $nowTime,
            'create_time' => $nowTime,
        ]);

        // 记录管理权限节点
        $recordButtons = [
            ['name' => 'expense/record/index', 'title' => '查看', 'type' => 'button'],
            ['name' => 'expense/record/add', 'title' => '添加', 'type' => 'button'],
            ['name' => 'expense/record/edit', 'title' => '编辑', 'type' => 'button'],
            ['name' => 'expense/record/del', 'title' => '删除', 'type' => 'button'],
            ['name' => 'expense/record/statistics', 'title' => '统计', 'type' => 'button'],
        ];

        foreach ($recordButtons as $button) {
            Db::name('admin_rule')->insert([
                'pid' => $recordMenuId,
                'type' => $button['type'],
                'title' => $button['title'],
                'name' => $button['name'],
                'path' => '',
                'icon' => '',
                'menu_type' => '',
                'extend' => 'none',
                'remark' => '',
                'weigh' => 0,
                'status' => 'normal',
                'update_time' => $nowTime,
                'create_time' => $nowTime,
            ]);
        }

        // 统计分析
        $statisticsMenuId = Db::name('admin_rule')->insertGetId([
            'pid' => $expenseMenuId,
            'type' => 'menu',
            'title' => '统计分析',
            'name' => 'expense/statistics',
            'path' => 'expense/statistics',
            'icon' => 'fa fa-bar-chart',
            'menu_type' => 'tab',
            'extend' => 'none',
            'remark' => '收支统计分析',
            'weigh' => 3,
            'status' => 'normal',
            'update_time' => $nowTime,
            'create_time' => $nowTime,
        ]);

        // 统计分析权限节点
        $statisticsButtons = [
            ['name' => 'expense/statistics/index', 'title' => '查看', 'type' => 'button'],
            ['name' => 'expense/statistics/getUserStats', 'title' => '用户统计', 'type' => 'button'],
            ['name' => 'expense/statistics/getCategoryStats', 'title' => '分类统计', 'type' => 'button'],
            ['name' => 'expense/statistics/getMonthlyTrend', 'title' => '月度趋势', 'type' => 'button'],
            ['name' => 'expense/statistics/getDailyStats', 'title' => '日统计', 'type' => 'button'],
            ['name' => 'expense/statistics/export', 'title' => '导出', 'type' => 'button'],
        ];

        foreach ($statisticsButtons as $button) {
            Db::name('admin_rule')->insert([
                'pid' => $statisticsMenuId,
                'type' => $button['type'],
                'title' => $button['title'],
                'name' => $button['name'],
                'path' => '',
                'icon' => '',
                'menu_type' => '',
                'extend' => 'none',
                'remark' => '',
                'weigh' => 0,
                'status' => 'normal',
                'update_time' => $nowTime,
                'create_time' => $nowTime,
            ]);
        }

        // 为超级管理员组添加权限
        $adminGroupId = 1; // 假设超级管理员组ID为1
        $adminGroup = Db::name('admin_group')->where('id', $adminGroupId)->find();
        if ($adminGroup) {
            $rules = $adminGroup['rules'] ? explode(',', $adminGroup['rules']) : [];
            
            // 获取所有新增的权限ID
            $newRules = Db::name('admin_rule')
                ->where('create_time', $nowTime)
                ->column('id');
            
            $rules = array_merge($rules, $newRules);
            $rules = array_unique($rules);
            
            Db::name('admin_group')->where('id', $adminGroupId)->update([
                'rules' => implode(',', $rules),
                'update_time' => $nowTime
            ]);
        }
    }

    /**
     * 回滚操作
     */
    public function down(): void
    {
        // 删除收支管理相关的所有菜单和权限
        $expenseMenuId = Db::name('admin_rule')
            ->where('name', 'expense')
            ->where('type', 'menu_dir')
            ->value('id');
        
        if ($expenseMenuId) {
            // 删除所有子菜单和权限节点
            $this->deleteMenuAndChildren($expenseMenuId);
        }
    }

    /**
     * 递归删除菜单及其子项
     */
    private function deleteMenuAndChildren($menuId): void
    {
        // 获取所有子菜单
        $children = Db::name('admin_rule')->where('pid', $menuId)->column('id');
        
        // 递归删除子菜单
        foreach ($children as $childId) {
            $this->deleteMenuAndChildren($childId);
        }
        
        // 删除当前菜单
        Db::name('admin_rule')->where('id', $menuId)->delete();
    }
}
