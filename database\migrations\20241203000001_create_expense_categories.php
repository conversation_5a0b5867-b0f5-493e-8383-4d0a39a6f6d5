<?php

use think\migration\Migrator;
use Phinx\Db\Adapter\MysqlAdapter;

class CreateExpenseCategories extends Migrator
{
    /**
     * 创建收支分类表
     */
    public function change(): void
    {
        $table = $this->table('expense_categories', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '收支分类表',
        ]);

        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '分类ID'
        ])
        ->addColumn('name', 'string', [
            'limit' => 50,
            'null' => false,
            'comment' => '分类名称'
        ])
        ->addColumn('type', 'enum', [
            'values' => ['income', 'expense'],
            'null' => false,
            'comment' => '分类类型:income=收入,expense=支出'
        ])
        ->addColumn('icon', 'string', [
            'limit' => 50,
            'null' => true,
            'default' => '',
            'comment' => '分类图标'
        ])
        ->addColumn('color', 'string', [
            'limit' => 20,
            'null' => true,
            'default' => '',
            'comment' => '分类颜色'
        ])
        ->addColumn('sort', 'integer', [
            'signed' => false,
            'null' => false,
            'default' => 0,
            'comment' => '排序权重'
        ])
        ->addColumn('status', 'enum', [
            'values' => ['enable', 'disable'],
            'null' => false,
            'default' => 'enable',
            'comment' => '状态:enable=启用,disable=禁用'
        ])
        ->addColumn('create_time', 'biginteger', [
            'signed' => false,
            'null' => true,
            'comment' => '创建时间'
        ])
        ->addColumn('update_time', 'biginteger', [
            'signed' => false,
            'null' => true,
            'comment' => '更新时间'
        ])
        ->addIndex(['type'], ['name' => 'idx_type'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['sort'], ['name' => 'idx_sort'])
        ->create();
    }
}
