export default {
    show: 'Show in Table Columns',
    width: 'Width',
    sortable: 'sortable',
    operator: 'Search operator',
    render: 'table column render',
    timeFormat: 'Format',
    step: 'Step',
    rows: 'Rows',
    'api url': 'api url',
    'api url example': 'For example: /admin/user.User/index',
    'remote-pk': 'value field',
    'remote-field': 'label field',
    'remote-url': 'remote URL',
    'remote-table': 'remote table',
    'remote-controller': 'remote controller',
    'remote-model': 'remote model',
    'remote-primary-table-alias': 'primary table alias',
    'relation-fields': 'relation fields',
    'image-multi': 'Multiple upload',
    'file-multi': 'Multiple upload',
    'select-multi': 'Multiple',
    validator: 'validator',
    validatorMsg: 'validator error message',
    copy: 'Copy',
    'CRUD record': 'CRUD record',
    'Delete Code': 'Delete Code',
    'Start CRUD design with this record?': 'Start CRUD design with this record?',
    'Are you sure to delete the generated CRUD code?': 'Are you sure to delete the generated CRUD code?',
    start: 'Start',
    create: 'Create',
    'New background CRUD from zero': 'New background CRUD from zero',
    'Select Data Table': 'Select Data Table',
    'Select a designed data table from the database': 'Select a designed data table from the database',
    'Start with previously generated CRUD code': 'Start with previously generated CRUD code',
    'Fast experience': 'Fast experience',
    'Please enter SQL': 'Please enter SQL',
    'Please select a data table': 'Please select a data table',
    'data sheet help': 'The table prefix must be the same as the table prefix configured for the project',
    'data sheet': 'data sheet',
    'table create SQL': 'table creation SQL',
    'Please enter the table creation SQL': 'Please enter the table creation SQL',
    'experience 1 1': 'Prepare the ',
    'experience 1 2': 'development environment',
    'experience 1 3': '(The site port is 1818)',
    'experience 2 1': 'On this page, click to',
    'experience 2 2': 'select data table',
    'experience 2 3': 'and select',
    'experience 3 1': 'Click',
    'experience 3 2': 'Generate CRUD Code',
    'experience 3 3': ', and click ',
    'experience 3 4': 'Continue to Generate',
    'experience 4 1': 'You are not currently in the development environment, ',
    'experience 4 2': 'please set up the development environment',
    'experience 4 3': ', or after generating the code, click on the upper right corner of the terminal to',
    'experience 4 4': 'Republish',
    // design
    'Name of the data table': 'Name of the data table',
    'Data Table Notes': 'Data Table Notes',
    'Generate CRUD code': 'Generate CRUD code',
    'give up': 'give up',
    'Table Quick Search Fields': 'Table Quick Search Fields',
    'Table Default Sort Fields': 'Table Default Sort Fields',
    'sort order': 'sort order',
    'sort order asc': 'asc',
    'sort order desc': 'desc',
    'Fields as Table Columns': 'Fields as Table Columns',
    'Fields as form items': 'Fields as form items',
    'The relative path to the generated code': 'The relative path to the generated code',
    'For quick combination code generation location, please fill in the relative path':
        'For quick combination code generation location, please fill in the relative path',
    'Generated Controller Location': 'Generated Controller Location',
    'Generated Data Model Location': 'Generated Data Model Location',
    'Generated Validator Location': 'Generated Validator Location',
    'Common model': 'Common model',
    'WEB end view directory': 'WEB end view directory',
    'Check model class': "Check whether protected $connection = '{connection}'; is configured in the above data model class",
    'There is no connection attribute in model class': 'If no configuration is available, you can configure it manually',
    'Advanced Configuration': 'Advanced Configuration',
    'Common Fields': 'Common Fields',
    'Base Fields': 'Base Fields',
    'Advanced Fields': 'Advanced Fields',
    'Field Name': 'Field Name',
    'field comment': 'field comment',
    'Please select a field from the left first': 'Please select a field from the left first',
    Common: 'Common',
    'Generate type': 'generate type',
    'Field comments (CRUD dictionary)': 'Field comments (CRUD dictionary)',
    'Field Properties': 'Field Properties',
    'Field Type': 'Field Type',
    length: 'length',
    'decimal point': 'decimal point',
    'Field Defaults': 'Field Defaults',
    'Please input the default value': 'Please input the default value',
    'Auto increment': 'Auto increment',
    Unsigned: 'Unsigned',
    'Allow NULL': 'Allow NULL',
    'Field Form Properties': 'Field Form Properties',
    'Field Table Properties': 'Field Table Properties',
    'Remote drop-down association information': 'Remote drop-down association information',
    'Associated Data Table': 'Associated Data Table',
    'Drop down value field': 'Drop down value field',
    'Drop down label field': 'Drop down label field',
    'Please select the value field of the select component': 'Please select the value field of the select component',
    'Please select the label field of the select component': 'Please select the label field of the select component',
    'Fields displayed in the table': 'Fields displayed in the table',
    'Please select the fields displayed in the table': 'Please select the fields displayed in the table',
    'Controller position': 'Controller position',
    'Please select the controller of the data table': 'Please select the controller of the data table',
    'Data Model Location': 'Data Model Location',
    'Data source configuration type': 'Data source configuration type',
    'Fast configuration with generated controllers and models': 'Fast configuration with generated controllers and models',
    'Custom configuration': 'Custom configuration',
    'If the remote interface query involves associated query of multiple tables, enter the alias of the primary data table here':
        'If the remote interface query involves associated query of multiple tables, enter the alias of the primary data table here',
    'Please select the data model location of the data table': 'Please select the data model location of the data table',
    'Confirm CRUD code generation': 'Confirm CRUD code generation',
    'Continue building': 'Continue building',
    'Please enter the data table name!': 'Please enter the data table name!',
    'Please enter the correct table name!': 'Please enter the correct table name!',
    'Use lower case underlined for table names': 'Use lower case underlined for table names',
    'Please design the primary key field!': 'Please design the primary key field!',
    'It is irreversible to give up the design Are you sure you want to give up?':
        'It is irreversible to give up the design. Are you sure you want to give up?',
    'There can only be one primary key field': 'There can only be one primary key field.',
    'Drag the left element here to start designing CRUD': 'Drag the left element here to start designing CRUD',
    'The data table already exists Continuing to generate will automatically delete the original table and create a new one!':
        'The data table already exists Continuing to generate will automatically delete the original table and create a new one!',
    'The controller already exists Continuing to generate will automatically overwrite the existing code!':
        'The controller already exists Continuing to generate will automatically overwrite the existing code!',
    'The menu rule with the same name already exists The menu and permission node will not be created in this generation':
        'The menu rule with the same name already exists The menu and permission node will not be created in this generation',
    'For example: `user table` will be generated into `user management`': 'For example: `user table` will be generated into `user management`',
    'The remote pull-down will request the corresponding controller to obtain data, so it is recommended that you create the CRUD of the associated table':
        'The remote pull-down will request the corresponding controller to obtain data, so it is recommended that you create the CRUD of the associated table',
    'If it is left blank, the model of the associated table will be generated automatically If the table already has a model, it is recommended to select it to avoid repeated generation':
        'If it is left blank, the model of the associated table will be generated automatically If the table already has a model, it is recommended to select it to avoid repeated generation',
    'The field comment will be used as the CRUD dictionary, and will be identified as the field title before the colon, and as the data dictionary after the colon':
        'The field comment will be used as the CRUD dictionary, and will be identified as the field title before the colon, and as the data dictionary after the colon',
    'Field name is invalid It starts with a letter or underscore and cannot contain any character other than letters, digits, or underscores':
        'Field name {field} is invalid. It starts with a letter or underscore and cannot contain any character other than letters, digits, or underscores',
    'The selected table has already generated records You are advised to start with historical records':
        'The selected table has already generated records. You are advised to start with historical records',
    'Start with the historical record': 'Start with the historical record',
    'Add field': 'Add field',
    'Modify field properties': 'Modify field properties',
    'Modify field name': 'Modify field name',
    'Delete field': 'Delete field',
    'Modify field order': 'Modify field order',
    'First field': 'First field',
    After: 'after',
    'Table design change': 'Table design change',
    'Data table design changes preview': 'Data table design changes preview',
    designChangeTips: 'When unchecked, the change will not be synchronized to the data table (the table structure has been manually modified, etc)',
    tableReBuild: 'Delete and rebuild',
    tableReBuildBlockHelp:
        'Deleting existing data tables and rebuilding them without adjusting the table structure ensures that CRUD code/records are consistent with the table structure',
    Yes: 'Yes',
    No: 'No',
    'If the data is abnormal, repeat the previous step': 'If the data is abnormal, repeat the previous step',
    'Field name duplication': 'field name {field} is duplicate',
    'Rename failed': 'Rename failed',
    'Design remote select tips':
        'The name of the cost field is automatically generated from the table name; Confirm that when the field name user_id is generated, the association method generated by the field name user_id is named user, and the association method generated by the field name developer_done_id is named developerDone. Note that the name prefix of the remote drop-down field is not the same',
    'Vite hot warning':
        'Vite Hot Update service not found, please generate code in the development environment, or click the WEB terminal in the upper right corner to republish',
    'Reset generate type attr':
        'The field generation type has been changed. Do you want to reset the field design to the preset scheme for the new type?',
    'Design efficiency': 'Determine design validity by yourself',
}
