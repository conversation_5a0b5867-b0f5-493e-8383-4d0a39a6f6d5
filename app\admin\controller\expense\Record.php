<?php

namespace app\admin\controller\expense;

use app\common\controller\Backend;
use app\admin\model\ExpenseRecord;
use app\admin\model\ExpenseCategory;
use app\admin\model\User;
use think\db\exception\PDOException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use Throwable;

/**
 * 收支记录管理
 */
class Record extends Backend
{
    /**
     * 模型对象
     */
    protected object $model;

    /**
     * 快速搜索字段
     */
    protected array $quickSearchField = ['description'];

    /**
     * 预处理
     */
    public function initialize(): void
    {
        parent::initialize();
        $this->model = new ExpenseRecord();
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        $res = $this->model
            ->withJoin([
                'user' => ['nickname'],
                'category' => ['name', 'type', 'icon', 'color']
            ])
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 添加
     * @throws Throwable
     */
    public function add(): void
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data = $this->excludeFields($data);
            
            // 设置默认值
            if (!isset($data['record_date']) || !$data['record_date']) {
                $data['record_date'] = date('Y-m-d');
            }
            if (!isset($data['record_time']) || !$data['record_time']) {
                $data['record_time'] = date('H:i:s');
            }

            $result = false;
            try {
                // 模型验证
                if ($this->modelValidate) {
                    $validate = str_replace('\\model\\', '\\validate\\', get_class($this->model));
                    if (class_exists($validate)) {
                        $validate = new $validate();
                        if ($this->modelSceneValidate) $validate->scene('add');
                        $validate->check($data);
                    }
                }
                $result = $this->model->save($data);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            } catch (PDOException $e) {
                $this->error($e->getMessage());
            }

            if ($result !== false) {
                $this->success(__('Added successfully'));
            } else {
                $this->error(__('No rows were added'));
            }
        }

        // 返回表单数据
        $this->success('', [
            'categories' => ExpenseCategory::getOptions(),
            'users' => User::field('id,nickname')->select()->toArray()
        ]);
    }

    /**
     * 编辑
     * @param int|string|null $id 主键
     * @throws Throwable
     */
    public function edit(int|string $id = null): void
    {
        $row = $this->model->find($id);
        if (!$row) {
            $this->error(__('Record not found'));
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data   = $this->excludeFields($data);
            $result = false;
            try {
                // 模型验证
                if ($this->modelValidate) {
                    $validate = str_replace('\\model\\', '\\validate\\', get_class($this->model));
                    if (class_exists($validate)) {
                        $validate = new $validate();
                        if ($this->modelSceneValidate) $validate->scene('edit');
                        $validate->check($data);
                    }
                }
                $result = $row->save($data);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            } catch (PDOException $e) {
                $this->error($e->getMessage());
            }

            if ($result !== false) {
                $this->success(__('Update successful'));
            } else {
                $this->error(__('No rows updated'));
            }
        }

        $this->success('', [
            'row' => $row,
            'categories' => ExpenseCategory::getOptions(),
            'users' => User::field('id,nickname')->select()->toArray()
        ]);
    }

    /**
     * 删除
     * @param array $ids 主键数组
     * @throws Throwable
     */
    public function del(array $ids = []): void
    {
        if (!$this->request->isDelete() || !$ids) {
            $this->error(__('Parameter error'));
        }

        $deleteCount = 0;
        try {
            $deleteCount = $this->model->where('id', 'in', $ids)->delete();
        } catch (PDOException $e) {
            $this->error($e->getMessage());
        }

        if ($deleteCount) {
            $this->success(__('Deleted successfully'));
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 获取统计数据
     */
    public function statistics(): void
    {
        $userId = $this->request->param('user_id', 0);
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        
        if (!$userId) {
            $this->error(__('Parameter error'));
        }
        
        $stats = ExpenseRecord::getUserStats($userId, $startDate, $endDate);
        
        $this->success('', $stats);
    }
}
